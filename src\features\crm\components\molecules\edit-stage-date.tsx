import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { OrderPartialUpdate, OrderStage, RetrieveOrder } from "../../types/order";
import { EditIcon } from "lucide-react";
import { useState } from "react";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";
import { useMutation } from "@tanstack/react-query";
import { partialUpdateOrder } from "../../services/portals/order";
import queryClient from "@lib/queryClient";

type EditStageDatePopoverProps = {
    stage: OrderStage;
    order: RetrieveOrder;
};

const EditStageDatePopover = ({ stage, order }: EditStageDatePopoverProps) => {
    const [open, setOpen] = useState(false);
    const { message, notification } = App.useApp();

    const [stageDate, setStageDate] = useState<Dayjs | null>(
        order?.stagesDates.find((s) => s.stage === stage)?.date
            ? dayjs(order?.stagesDates.find((s) => s.stage === stage)?.date)
            : null,
    );

    const { mutate: updateStageUpdateMutate, isPending } = useMutation({
        mutationFn: (payload: OrderPartialUpdate) =>
            partialUpdateOrder(order.oid, payload),
        onSuccess: () => {
            message.success({
                content: "Fecha de etapa actualizada",
                duration: 2,
            });
            queryClient.invalidateQueries({
                queryKey: ["order", order.oid],
            });
            setOpen(false);
            setStageDate(null);
        },
        onError: () => {
            notification.error({
                message: "Error al actualizar la fecha de etapa",
                description:
                    "Ha ocurrido un error al intentar actualizar la fecha de etapa",
                duration: 2,
            });
        },
    });

    const handleOpenChange = (newOpen: boolean) => {
        setOpen(newOpen);
    };

    const handleUpdateStageDate = () => {
        if (stageDate) {
            let payload = {};
            if (stage === OrderStage.PROSPECT) {
                payload = {
                    prospectedAt: stageDate.toISOString(),
                };
            } else if (stage === OrderStage.INTERESTED) {
                payload = {
                    interestedAt: stageDate.toISOString(),
                };
            } else if (stage === OrderStage.TO_PAY) {
                payload = {
                    toPayAt: stageDate.toISOString(),
                };
            } else if (stage === OrderStage.SOLD) {
                payload = {
                    paidAt: stageDate.toISOString(),
                };
            } else if (stage === OrderStage.LOST) {
                payload = {
                    lostAt: stageDate.toISOString(),
                };
            }
            return updateStageUpdateMutate({
                ...payload,
                owner: order.owner.uid,
            });
        }
        setOpen(false);
    };
    return (
        <Popover
            content={
                <>
                    <DatePicker
                        showTime
                        value={stageDate}
                        onChange={(date) => setStageDate(date)}
                        format="YYYY-MM-DD HH:mm:ss"
                    />
                    <Divider className="my-1" />
                    <div className="flex">
                        <Button
                            block
                            type="text"
                            onClick={() => setOpen(false)}
                            disabled={isPending}
                        >
                            Cancelar
                        </Button>
                        <Button
                            block
                            type="primary"
                            onClick={handleUpdateStageDate}
                            loading={isPending}
                        >
                            Actualizar
                        </Button>
                    </div>
                </>
            }
            trigger="click"
            open={open}
            onOpenChange={handleOpenChange}
        >
            <Tooltip title="Editar fecha de etapa">
                <EditIcon className="text-blue-full hover:cursor-pointer" size={16} />
            </Tooltip>
        </Popover>
    );
};

export default EditStageDatePopover;
