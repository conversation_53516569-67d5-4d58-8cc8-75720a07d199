import { OrderItemOffering } from "../../types/order";
import { Book, ExternalLink, Tag as TagIcon } from "lucide-react";
import { Link } from "react-router-dom";
import { Tooltip, Typography, Tag } from "antd";

const { Text } = Typography;

type OrderItemPriceCellProps = {
    content: OrderItemOffering;
};

export default function OrderItemOfferingCell({ content }: OrderItemPriceCellProps) {
    return (
        <div className="flex flex-col space-y-2">
            <div className="flex items-center gap-2">
                <Book size={16} className="text-blue-medium" strokeWidth={1.75} />
                <Text
                    className="font-medium text-black-full line-clamp-1"
                    title={content.name}
                >
                    {content.name}
                </Text>
            </div>

            <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                    <TagIcon
                        size={14}
                        className="text-black-medium"
                        strokeWidth={1.75}
                    />
                    <Tag
                        className="m-0 text-xs rounded-full"
                        bordered={false}
                        color="blue"
                    >
                        {content.slug}
                    </Tag>
                    <Tooltip title="Ver detalles del producto">
                        <Link
                            to={`/cms/offering/${content.oid}`}
                            className="flex items-center text-blue-full hover:text-blue-medium transition-colors"
                        >
                            <ExternalLink
                                size={14}
                                className="ml-2"
                                strokeWidth={1.75}
                            />
                        </Link>
                    </Tooltip>
                </div>
            </div>
        </div>
    );
}
