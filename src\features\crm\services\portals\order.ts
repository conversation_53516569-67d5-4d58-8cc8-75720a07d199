import { PaginatedResponse } from "@myTypes/base";
import {
    CreateOrder,
    Order,
    OrderPartialUpdate,
    RetrieveOrder,
} from "../../types/order";
import { portalsApi } from "@services/portals";

const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE = 1;

export type ListOrdersQueryParams = {
    page: number;
    pageSize: number;
    search?: string;
};

export const listOrders = async (
    queryParams: ListOrdersQueryParams = {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
    },
): Promise<PaginatedResponse<Order>> => {
    const response = await portalsApi.get("crm/orders", {
        params: queryParams,
    });
    return response.data;
};

export const getOrdersByContact = async (
    contactId: string,
): Promise<PaginatedResponse<Order>> => {
    const response = await portalsApi.get(`crm/orders`, {
        params: {
            owner: contactId,
        },
    });
    return response.data;
};

export const retrieveOrder = async (oid: string): Promise<RetrieveOrder> => {
    const response = await portalsApi.get(`crm/orders/${oid}`);
    return response.data;
};

export const createOrder = async (order: CreateOrder): Promise<Order> => {
    const response = await portalsApi.post("crm/orders", order);
    return response.data;
};

export const partialUpdateOrder = async (
    oid: string,
    order: Partial<OrderPartialUpdate>,
): Promise<Order> => {
    const response = await portalsApi.patch(`crm/orders/${oid}`, order);
    return response.data;
};

export const getOrderProducts = async (oid: string): Promise<Order> => {
    const response = await portalsApi.get(`crm/orders/${oid}/products`);
    return response.data;
};