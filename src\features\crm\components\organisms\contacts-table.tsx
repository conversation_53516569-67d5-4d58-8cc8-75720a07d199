import React from "react";
import { Card, Table, Tag, Avatar, Button } from "antd";
import {
    ContactListItem,
    ContactOcupation,
    ContactOcupationLabel,
} from "@/features/crm/types/contact";
import { UsersIcon, MoreHorizontal, Phone, Mail } from "lucide-react";

interface ContactsTableProps {
    contacts: ContactListItem[];
    loading?: boolean;
}

const ContactsTable: React.FC<ContactsTableProps> = ({ contacts, loading = false }) => {
    const columns = [
        {
            title: "Contacto",
            dataIndex: "fullName",
            key: "fullName",
            render: (_: string, record: ContactListItem) => (
                <div className="flex items-center gap-3">
                    <Avatar className="bg-blue-100 text-blue-600">
                        {record.firstName.charAt(0)}
                    </Avatar>
                    <div>
                        <div className="font-medium">{record.fullName}</div>
                        <div className="text-xs text-gray-500">
                            {record.ocupation === ContactOcupation.STUDENT
                                ? `${record.major || "Sin carrera"}, ${record.term || "Sin semestre"}`
                                : record.role || "Sin rol"}
                        </div>
                    </div>
                </div>
            ),
        },
        {
            title: "Ocupación",
            dataIndex: "ocupation",
            key: "ocupation",
            render: (ocupation: ContactOcupation) => {
                const colorMap = {
                    [ContactOcupation.STUDENT]: "green",
                    [ContactOcupation.EMPLOYEE]: "blue",
                    [ContactOcupation.INDEPENDENT]: "purple",
                };
                return (
                    <Tag color={colorMap[ocupation]}>
                        {ContactOcupationLabel[ocupation]}
                    </Tag>
                );
            },
        },
        {
            title: "Contacto",
            key: "contact",
            render: (_: string, record: ContactListItem) => (
                <div className="flex gap-2">
                    <Button
                        type="text"
                        icon={<Phone className="h-4 w-4" />}
                        className="flex items-center justify-center"
                        title={record.phoneNumber}
                    />
                    <Button
                        type="text"
                        icon={<Mail className="h-4 w-4" />}
                        className="flex items-center justify-center"
                        title={record.email}
                    />
                </div>
            ),
        },
        {
            title: "Estado",
            dataIndex: "isActive",
            key: "isActive",
            render: (isActive: boolean) => (
                <Tag color={isActive ? "green" : "red"}>
                    {isActive ? "Activo" : "Inactivo"}
                </Tag>
            ),
        },
        {
            title: "Última actualización",
            dataIndex: "updatedAt",
            key: "updatedAt",
            render: (date: string) => {
                const formatRelativeTime = (dateStr: string): string => {
                    const rtf = new Intl.RelativeTimeFormat("es", { numeric: "auto" });
                    const now = new Date();
                    const pastDate = new Date(dateStr);
                    const diffMs = now.getTime() - pastDate.getTime();

                    // Convertir a segundos
                    const diffSec = Math.floor(diffMs / 1000);

                    // Determinar la mejor unidad para mostrar el tiempo relativo
                    if (diffSec < 60) {
                        return rtf.format(-Math.floor(diffSec), "second");
                    } else if (diffSec < 3600) {
                        return rtf.format(-Math.floor(diffSec / 60), "minute");
                    } else if (diffSec < 86400) {
                        return rtf.format(-Math.floor(diffSec / 3600), "hour");
                    } else if (diffSec < 604800) {
                        return rtf.format(-Math.floor(diffSec / 86400), "day");
                    } else if (diffSec < 2592000) {
                        return rtf.format(-Math.floor(diffSec / 604800), "week");
                    } else if (diffSec < 31536000) {
                        return rtf.format(-Math.floor(diffSec / 2592000), "month");
                    } else {
                        return rtf.format(-Math.floor(diffSec / 31536000), "year");
                    }
                };

                return (
                    <span className="text-gray-500">{formatRelativeTime(date)}</span>
                );
            },
        },
        {
            title: "Acciones",
            key: "actions",
            render: () => (
                <Button
                    type="text"
                    icon={<MoreHorizontal className="h-4 w-4" />}
                    className="flex items-center justify-center"
                />
            ),
        },
    ];

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <UsersIcon className="h-5 w-5 text-blue-500" />
                    <span>Contactos</span>
                </div>
            }
            className="shadow-md"
        >
            <Table
                columns={columns}
                dataSource={contacts}
                rowKey="uid"
                pagination={{ pageSize: 20 }}
                loading={loading}
                className="contactsTable"
            />
        </Card>
    );
};

export default ContactsTable;
