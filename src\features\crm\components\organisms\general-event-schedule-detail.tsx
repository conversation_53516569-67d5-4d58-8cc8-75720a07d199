import {
    EventModality,
    EventModalityLabels,
    EventStage,
    EventStageLabels,
} from "@/features/crm/types/event";
import {
    EventSchedule,
    UpdateEventScheduleFormValues,
} from "@/features/crm/types/event-schedule";
import { Button, DatePicker, Form, Input, Select } from "antd";
import { Save, Trash } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import SelectInstructor from "@/features/crm/components/molecules/select-instructor";
import { updateEventSchedule } from "../../services/portals/event-schedule";
import dayjs from "dayjs";
import SelectPartnership from "../molecules/select-partnership";
import UploadEventScheduleThumbnail from "../molecules/upload-event-schedule-thumbnail";
import UploadEventScheduleCover from "../molecules/upload-event-schedule-cover";

const { TextArea } = Input;

type GeneralEventDetailProps = {
    eventSchedule: EventSchedule;
};

export default function GeneralEventDetail({ eventSchedule }: GeneralEventDetailProps) {
    const [form] = Form.useForm<UpdateEventScheduleFormValues>();
    const queryClient = useQueryClient();

    const { mutate: onEventUpdate, isPending: isEventUpdateLoading } = useMutation({
        mutationFn: (partialEvent: Partial<UpdateEventScheduleFormValues>) =>
            updateEventSchedule(eventSchedule.esid, partialEvent),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ["event-schedule", eventSchedule.esid],
            });
        },
    });

    const handleFormFinish = (values: UpdateEventScheduleFormValues) => {
        onEventUpdate(values);
    };

    const modalitySelectOptions = Object.values(EventModality).map((value) => ({
        value,
        label: EventModalityLabels[value],
    }));

    const stageSelectOptions = Object.values(EventStage).map((value) => ({
        value,
        label: EventStageLabels[value],
    }));

    return (
        <Form
            name="generalEventForm"
            layout="vertical"
            form={form}
            initialValues={{
                name: eventSchedule.name,
                description: eventSchedule.description,
                modality: eventSchedule.modality,
                stage: eventSchedule.stage,
                instructor: eventSchedule.instructor?.iid,
                location: eventSchedule.location,
                price: eventSchedule.price,
                partnerships: eventSchedule.partnerships.map((p) => p.pid),
                rangeDate: [
                    dayjs(eventSchedule.startDate),
                    dayjs(eventSchedule.endDate),
                ],
            }}
            onFinish={handleFormFinish}
        >
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                <div className="bg-white-full col-span-4 p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm">
                        INFORMACIÓN GENERAL
                    </p>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4">
                        <Form.Item<UpdateEventScheduleFormValues>
                            name="name"
                            label={<FormLabel>Nombre del evento</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese el nombre del evento.",
                                },
                            ]}
                        >
                            <Input placeholder="Ej. ¿Cómo ingresar a los CEUs?" />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="modality"
                            label={<FormLabel>Modalidad</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione la modalidad.",
                                },
                            ]}
                        >
                            <Select options={modalitySelectOptions} />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="stage"
                            label={<FormLabel>Etapa</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione la etapa.",
                                },
                            ]}
                        >
                            <Select options={stageSelectOptions} />
                        </Form.Item>
                        <Form.Item<UpdateEventScheduleFormValues>
                            name="partnerships"
                            label={<FormLabel>Alianzas</FormLabel>}
                        >
                            <SelectPartnership />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="description"
                            label={<FormLabel>Descripción</FormLabel>}
                            className="col-span-2"
                        >
                            <TextArea
                                rows={4}
                                placeholder="Descripción del evento..."
                            />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="location"
                            label={<FormLabel>Ubicación</FormLabel>}
                        >
                            <Input placeholder="Ej. Zoom, Google Meet, etc." />
                        </Form.Item>
                        <Form.Item
                            name="instructor"
                            label={<FormLabel>Instructor</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione un instructor.",
                                },
                            ]}
                        >
                            <SelectInstructor />
                        </Form.Item>
                    </div>
                </div>

                <div className="col-span-2 space-y-2">
                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">ACCIONES</p>
                        <div className="flex justify-end gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                block
                            >
                                Eliminar
                            </Button>
                            <Button
                                block
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Save />}
                                disabled={isEventUpdateLoading}
                                onClick={() => form.submit()}
                            >
                                Guardar
                            </Button>
                        </div>
                    </div>

                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">FECHAS</p>
                        <div>
                            <Form.Item<UpdateEventScheduleFormValues>
                                name="rangeDate"
                                label={<FormLabel>Fecha de Inicio/Fin</FormLabel>}
                            >
                                <DatePicker.RangePicker showTime className="w-full" />
                            </Form.Item>
                        </div>
                    </div>

                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">
                            CONTENIDO MULTIMEDIA
                        </p>
                        <div>
                            <FormLabel>Miniatura</FormLabel>
                            <UploadEventScheduleThumbnail
                                esid={eventSchedule.esid}
                                initialEventScheduleThumbnail={eventSchedule.thumbnail}
                            />
                        </div>

                        <div>
                            <FormLabel>Portada</FormLabel>
                            <UploadEventScheduleCover
                                esid={eventSchedule.esid}
                                initialEventScheduleCoverImage={
                                    eventSchedule.coverImage
                                }
                            />
                        </div>
                    </div>
                </div>
            </div>
        </Form>
    );
}
