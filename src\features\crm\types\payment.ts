import { UploadFile } from "antd";
import { Dayjs } from "dayjs";
import { OrderStage } from "./order";

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 20;

export type ListContactsQuery = {
    status: string;
};

export type ListPaymentsQueryParams = {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    isPaid?: string;
    order?: string;
};

export type PaymentOrder = {
    oid: string;
    stage: OrderStage;
    owner: {
        uid: string;
        fullName: string;
        email?: string;
        phoneNumber: string;
    };
};

export type PaymentVoucher = {
    fid: string;
    name: string;
    url: string;
    contentType: string;
};

export enum PaymentCurrency {
    USD = "usd",
    PEN = "pen",
}

type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

export type PaymentListItem = AuditBaseType & {
    key: string;
    pid: string;
    order: PaymentOrder;
    paymentMethod: {
        pmid: string;
        name: string;
    };
    paymentDate: string;
    amount: number;
    currency: PaymentCurrency;
    isPaid: boolean;
};

export type PaymentRetrieve = AuditBaseType & {
    pid: string;
    order: PaymentOrder;
    paymentMethod?: {
        pmid: string;
        name: string;
    };
    isPaid: boolean;
    paymentDate: string;
    voucher: PaymentVoucher;
    amount: number;
    currency: PaymentCurrency;
};

export type PaymentUpdateForm = {
    paymentMethod: string;
    isPaid: boolean;
    amount: number;
    currency: PaymentCurrency;
    paymentDate: Dayjs;
};

export type PaymentCreateForm = {
    order: string;
    amount: number;
    currency: PaymentCurrency;
    isPaid: boolean;

    paymentDate?: Dayjs;
    voucher?: UploadFile[];
    paymentMethod?: string;
};

export type PaymentCreateRequest = {
    order: string;
    amount: number;
    currency: PaymentCurrency;
    isPaid: boolean;
    paymentDate?: string;
    voucher?: string;
    paymentMethod?: string;
};