import { getContacts, retrieveContact } from "@/features/crm/services/portals/contact";
import { useQuery } from "@tanstack/react-query";

type UseContactsQuery = {
    isStaff: boolean;
};

type UseContactsProps = {
    page?: number;
    pageSize?: number;
    query?: UseContactsQuery;
};

export const useContacts = ({ query }: UseContactsProps) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["contacts", query],
        queryFn: () => getContacts(query?.isStaff ? { isStaff: query.isStaff } : {}),
        refetchOnWindowFocus: false,
    });

    const { count: COUNT, results: contacts } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        contacts,
        COUNT,
    };
};

export const useContact = (contactId: string) => {
    const {
        data: contact,
        isLoading,
        isError,
    } = useQuery({
        queryKey: ["contact", contactId],
        queryFn: () => retrieveContact(contactId),
        enabled: !!contactId,
        refetchOnWindowFocus: false,
    });

    return {
        contact,
        isLoading,
        isError,
    };
};
