import { PaymentOrder } from "../../types/payment";
import { Link } from "react-router-dom";
import { Mail, Phone, ExternalLink } from "lucide-react";
import { OrderStage, OrderStageLabels } from "../../types/order";

type PaymentOrderCellProps = {
    order: PaymentOrder;
};

export default function PaymentOrderCell({ order }: PaymentOrderCellProps) {
    const { owner, oid, stage } = order;

    const getBadgeColor = () => {
        switch (stage) {
            case OrderStage.PROSPECT:
                return "bg-blue-100 text-blue-800";
            case OrderStage.INTERESTED:
                return "bg-green-100 text-green-800";
            case OrderStage.TO_PAY:
                return "bg-yellow-100 text-yellow-800";
            case OrderStage.PAID:
                return "bg-purple-100 text-purple-800";
            case OrderStage.LOST:
                return "bg-red-100 text-red-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    return (
        <div className="px-1 rounded-md hover:bg-gray-50 transition-colors">
            <div className="flex items-center">
                <Link
                    to={`/crm/orders/${oid}`}
                    className="flex items-center text-blue-600 hover:text-blue-800"
                >
                    <span className="text-sm font-medium mr-1">#{oid.slice(-6)}</span>
                    <ExternalLink size={14} />
                </Link>
            </div>

            <p className="text-sm font-medium">
                {owner.fullName}{" "}
                <span className={`text-xs px-2 py-0.5 rounded-full ${getBadgeColor()}`}>
                    {OrderStageLabels[stage]}
                </span>
            </p>

            <div className="flex gap-2">
                {owner.email && (
                    <p className="text-xs text-gray-500 flex items-center mt-1">
                        <Mail size={14} className="h-3.5 w-3.5 mr-1" />
                        {owner.email}
                    </p>
                )}

                <p className="text-xs text-gray-500 flex items-center mt-1">
                    <Phone size={14} className="h-3.5 w-3.5 mr-1" />
                    {owner.phoneNumber}
                </p>
            </div>
        </div>
    );
}
