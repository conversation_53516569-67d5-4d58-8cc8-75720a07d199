import { PaginatedResponse, PaginationParams } from "@myTypes/base";
import { BlogPost, BlogStatus, UpdateBlogPostBody } from "@myTypes/blog";
import { portalsApi } from "@services/portals";

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;

export type ListBlogPostsQuery = PaginationParams & {
    filters?: Partial<{
        search: string;
        status: BlogStatus;
        featured: boolean;
        categoryId: string;
        tagId: string;
        authorId: string;
        sortBy: string;
        order: "asc" | "desc";
    }>;
};

/**
 * List blog posts with pagination and filtering
 */
export const getBlogPosts = async (
    params: ListBlogPostsQuery = {},
): Promise<PaginatedResponse<BlogPost>> => {
    const { page, pageSize, filters } = params;

    const response = await portalsApi.get("cms/blogs/posts", {
        params: {
            page: page || DEFAULT_PAGE,
            pageSize: pageSize || DEFAULT_PAGE_SIZE,
            ...filters,
        },
    });
    return response.data;
};

/**
 * List blog posts by status
 */
export const listBlogPostsByStatus = async (
    status: BlogStatus,
    params: Omit<ListBlogPostsQuery, "status"> = {},
): Promise<PaginatedResponse<BlogPost>> => {
    return getBlogPosts({ ...params, filters: { status } });
};

/**
 * Create a new blog post with empty content
 * Blog editors typically create an empty post first and then update it
 */
export const createBlogPost = async (): Promise<Pick<BlogPost, "bid">> => {
    const response = await portalsApi.post("cms/blogs/posts", {});
    return response.data;
};

/**
 * Retrieve a blog post by ID
 */
export const retrieveBlogPost = async (bid: string | undefined): Promise<BlogPost> => {
    if (!bid) {
        throw new Error("No blog post ID provided");
    }
    const response = await portalsApi.get(`cms/blogs/posts/${bid}`, {
        params: {
            excludeFromCamelize: ["content"],
        },
    });
    return response.data;
};

/**
 * Update a blog post
 */
export const updateBlogPost = async (
    bid: string | undefined,
    data: UpdateBlogPostBody,
): Promise<BlogPost> => {
    if (!bid) {
        throw new Error("No blog post ID provided");
    }

    const response = await portalsApi.patch(`cms/blogs/posts/${bid}`, data);
    return response.data;
};

/**
 * Delete a blog post
 */
export const deleteBlogPost = async (bid: string): Promise<void> => {
    if (!bid) {
        throw new Error("No blog post ID provided");
    }
    await portalsApi.delete(`cms/blogs/posts/${bid}`);
};

/**
 * Change blog post status to draft
 */
export const setPostToDraft = async (bid: string): Promise<BlogPost> => {
    return updateBlogPost(bid, { status: BlogStatus.DRAFT });
};

/**
 * Change blog post status to published
 */
export const publishPost = async (bid: string): Promise<BlogPost> => {
    return updateBlogPost(bid, {
        status: BlogStatus.PUBLISHED,
        publishedAt: new Date(),
    });
};

/**
 * Change blog post status to archived
 */
export const archivePost = async (bid: string): Promise<BlogPost> => {
    return updateBlogPost(bid, { status: BlogStatus.ARCHIVED });
};

/**
 * Toggle featured status of a blog post
 */
export const toggleFeaturedPost = async (
    bid: string,
    featured: boolean,
): Promise<BlogPost> => {
    return updateBlogPost(bid, { featured });
};
