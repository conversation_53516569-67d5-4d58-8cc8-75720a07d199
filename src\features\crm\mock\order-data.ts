import { Order, OrderStage } from "@/features/crm/types/order";
import { faker } from "@faker-js/faker";

// Utility function to create a random date in the last 12 months
const randomDate = (months = 12) => {
    const date = new Date();
    date.setMonth(date.getMonth() - Math.floor(Math.random() * months));
    date.setDate(Math.floor(Math.random() * 28) + 1);
    return date.toISOString();
};

// Utility function to create a random contact
const createContact = (customEmail?: string) => {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    return {
        uid: faker.string.uuid(),
        firstName,
        lastName,
        fullName: `${firstName} ${lastName}`,
        email: customEmail || faker.internet.email({ firstName, lastName }),
        phoneNumber: faker.phone.number(),
    };
};

// Create mock offerings
export const mockOfferings = [
    { oid: "off-1", name: "Curso de Marketing Digital", slug: "marketing-digital" },
    {
        oid: "off-2",
        name: "Maestría en Administración",
        slug: "maestria-administracion",
    },
    { oid: "off-3", name: "Diplomado en Finanzas", slug: "diplomado-finanzas" },
    { oid: "off-4", name: "Curso de SQL Avanzado", slug: "sql-avanzado" },
    {
        oid: "off-5",
        name: "Curso de Python para Data Science",
        slug: "python-data-science",
    },
    {
        oid: "off-6",
        name: "Diploma en Gestión de Proyectos",
        slug: "gestion-proyectos",
    },
    { oid: "off-7", name: "Curso de UX/UI Design", slug: "ux-ui-design" },
];

// Create mock sales agents
export const mockSalesAgents = [
    createContact("<EMAIL>"),
    createContact("<EMAIL>"),
    createContact("<EMAIL>"),
    createContact("<EMAIL>"),
];

// Generate mock order items
const createOrderItems = (count = 1) => {
    const items = [];

    for (let i = 0; i < count; i++) {
        const offering =
            mockOfferings[Math.floor(Math.random() * mockOfferings.length)];
        const basePrice =
            Math.round(faker.number.float({ min: 500, max: 5000 }) / 10) * 10;
        const foreignBasePrice = Math.round(basePrice / 3.8);
        const discount = Math.random() > 0.7 ? Math.round(Math.random() * 20) * 5 : 0;
        const unitPrice = basePrice - (basePrice * discount) / 100;
        const quantity = Math.floor(Math.random() * 2) + 1;

        items.push({
            oiid: faker.string.uuid(),
            key: faker.string.uuid(),
            offering,
            quantity,
            basePrice,
            foreignBasePrice,
            discount,
            unitPrice,
            totalPrice: unitPrice * quantity,
            foreignUnitPrice: Math.round(unitPrice / 3.8),
            createdAt: randomDate(),
            updatedAt: randomDate(1),
        });
    }

    return items;
};

// Function to generate mock orders with realistic stage transitions
const generateMockOrders = (count = 100): Order[] => {
    const orders: Order[] = [];

    // Distribution of orders across stages
    const stageDistribution = {
        [OrderStage.PROSPECT]: 0.35,
        [OrderStage.INTERESTED]: 0.25,
        [OrderStage.TO_PAY]: 0.15,
        [OrderStage.PAID]: 0.15,
        [OrderStage.LOST]: 0.1,
    };

    for (let i = 0; i < count; i++) {
        // Determine stage based on distribution
        let stage: OrderStage;
        const rand = Math.random();
        let cumulative = 0;

        for (const [stageKey, probability] of Object.entries(stageDistribution)) {
            cumulative += probability;
            if (rand <= cumulative) {
                stage = stageKey as OrderStage;
                break;
            }
        }

        // If somehow no stage was selected, default to PROSPECT
        if (!stage!) {
            stage = OrderStage.PROSPECT;
        }

        // Generate dates based on stage
        const createdAt = randomDate(12);
        const prospectAt = createdAt;

        let interestedAt = null;
        let toPayAt = null;
        let paidAt = null;
        let lostAt = null;

        if (
            stage === OrderStage.INTERESTED ||
            stage === OrderStage.TO_PAY ||
            stage === OrderStage.PAID
        ) {
            const interestedDate = new Date(createdAt);
            interestedDate.setDate(
                interestedDate.getDate() + Math.floor(Math.random() * 10) + 1,
            );
            interestedAt = interestedDate.toISOString();
        }

        if (stage === OrderStage.TO_PAY || stage === OrderStage.PAID) {
            const toPayDate = new Date(interestedAt!);
            toPayDate.setDate(toPayDate.getDate() + Math.floor(Math.random() * 15) + 1);
            toPayAt = toPayDate.toISOString();
        }

        if (stage === OrderStage.PAID) {
            const paidDate = new Date(toPayAt!);
            paidDate.setDate(paidDate.getDate() + Math.floor(Math.random() * 7) + 1);
            paidAt = paidDate.toISOString();
        }

        if (stage === OrderStage.LOST) {
            // Lost can happen at any stage
            const lostStage = Math.floor(Math.random() * 3); // 0: prospect, 1: interested, 2: to_pay

            if (lostStage >= 1) {
                const interestedDate = new Date(createdAt);
                interestedDate.setDate(
                    interestedDate.getDate() + Math.floor(Math.random() * 10) + 1,
                );
                interestedAt = interestedDate.toISOString();
            }

            if (lostStage >= 2) {
                const toPayDate = new Date(interestedAt!);
                toPayDate.setDate(
                    toPayDate.getDate() + Math.floor(Math.random() * 15) + 1,
                );
                toPayAt = toPayDate.toISOString();
            }

            // When it was lost depends on the stage at which it was lost
            const lostDate = new Date(
                lostStage === 0
                    ? createdAt
                    : lostStage === 1
                      ? interestedAt!
                      : toPayAt!,
            );
            lostDate.setDate(lostDate.getDate() + Math.floor(Math.random() * 5) + 1);
            lostAt = lostDate.toISOString();
        }

        // Update date is the latest stage date or created date
        const latestDate = [createdAt, interestedAt, toPayAt, paidAt, lostAt]
            .filter(Boolean)
            .sort()
            .pop()!;

        // Create order items (1-3 items per order)
        const orderItems = createOrderItems(Math.floor(Math.random() * 2) + 1);

        // Calculate total
        const totalPrice = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);

        // Sometimes have an agreed total that's different from the sum of items
        const agreedTotal =
            Math.random() > 0.3
                ? totalPrice - (Math.random() > 0.5 ? Math.round(totalPrice * 0.05) : 0)
                : undefined;

        const order: Order = {
            oid: faker.string.uuid(),
            owner: createContact(),
            stage,
            orderItems,
            prospectAt,
            interestedAt: interestedAt || undefined,
            toPayAt: toPayAt || undefined,
            paidAt: paidAt || undefined,
            lostAt: lostAt || undefined,
            agreedTotal,
            salesAgent:
                Math.random() > 0.2
                    ? mockSalesAgents[
                          Math.floor(Math.random() * mockSalesAgents.length)
                      ]
                    : undefined,
            createdAt,
            updatedAt: latestDate,
        };

        orders.push(order);
    }

    return orders;
};

// Create 150 mock orders
export const mockOrders = generateMockOrders(150);

// Calculate orders by stage for charts
export const ordersByStage = Object.values(OrderStage).map((stage) => ({
    name: stage,
    value: mockOrders.filter((order) => order.stage === stage).length,
}));

// Calculate funnel data
const prospectCount = mockOrders.filter((order) => !!order.prospectAt).length;
const interestedCount = mockOrders.filter((order) => !!order.interestedAt).length;
const toPayCount = mockOrders.filter((order) => !!order.toPayAt).length;
const paidCount = mockOrders.filter((order) => !!order.paidAt).length;

export const salesFunnelData = [
    {
        name: "Prospectos",
        value: prospectCount,
    },
    {
        name: "Interesados",
        value: interestedCount,
        rate: Math.round((interestedCount / prospectCount) * 100),
    },
    {
        name: "Por pagar",
        value: toPayCount,
        rate: Math.round((toPayCount / interestedCount) * 100),
    },
    {
        name: "Pagados",
        value: paidCount,
        rate: Math.round((paidCount / toPayCount) * 100),
    },
];

// Calculate orders by month
export const ordersByMonth = Array.from({ length: 12 }, (_, i) => {
    const date = new Date();
    date.setMonth(date.getMonth() - 11 + i);
    const monthYear = date.toISOString().substring(0, 7); // Format: YYYY-MM

    return {
        month: date.toLocaleDateString("es-ES", { month: "short" }),
        prospect: mockOrders.filter(
            (order) =>
                order.createdAt.startsWith(monthYear) &&
                order.stage === OrderStage.PROSPECT,
        ).length,
        interested: mockOrders.filter(
            (order) =>
                order.createdAt.startsWith(monthYear) &&
                order.stage === OrderStage.INTERESTED,
        ).length,
        toPay: mockOrders.filter(
            (order) =>
                order.createdAt.startsWith(monthYear) &&
                order.stage === OrderStage.TO_PAY,
        ).length,
        paid: mockOrders.filter(
            (order) =>
                order.createdAt.startsWith(monthYear) &&
                order.stage === OrderStage.PAID,
        ).length,
        lost: mockOrders.filter(
            (order) =>
                order.createdAt.startsWith(monthYear) &&
                order.stage === OrderStage.LOST,
        ).length,
        total: mockOrders.filter((order) => order.createdAt.startsWith(monthYear))
            .length,
    };
});

// Calculate revenue by month
export const revenueByMonth = Array.from({ length: 12 }, (_, i) => {
    const date = new Date();
    date.setMonth(date.getMonth() - 11 + i);
    const monthYear = date.toISOString().substring(0, 7); // Format: YYYY-MM

    // Get all paid orders from this month
    const paidOrdersThisMonth = mockOrders.filter((order) =>
        order.paidAt?.startsWith(monthYear),
    );

    // Calculate total revenue
    const revenue = paidOrdersThisMonth.reduce((sum, order) => {
        return (
            sum +
            (order.agreedTotal ||
                order.orderItems.reduce(
                    (itemSum, item) => itemSum + item.totalPrice,
                    0,
                ))
        );
    }, 0);

    return {
        month: date.toLocaleDateString("es-ES", { month: "short" }),
        revenue,
    };
});

// Calculate orders by offering
export const ordersByOffering = mockOfferings
    .map((offering) => {
        const count = mockOrders.filter((order) =>
            order.orderItems.some((item) => item.offering.oid === offering.oid),
        ).length;

        return {
            name: offering.name,
            value: count,
        };
    })
    .sort((a, b) => b.value - a.value)
    .slice(0, 5);

// Calculate total revenue
export const totalRevenue = mockOrders
    .filter((order) => order.stage === OrderStage.PAID)
    .reduce((sum, order) => {
        return (
            sum +
            (order.agreedTotal ||
                order.orderItems.reduce(
                    (itemSum, item) => itemSum + item.totalPrice,
                    0,
                ))
        );
    }, 0);

// Calculate conversion rates
export const conversionRates = {
    prospectToInterested: Math.round(
        (salesFunnelData[1].value / salesFunnelData[0].value) * 100,
    ),
    interestedToPay: Math.round(
        (salesFunnelData[2].value / salesFunnelData[1].value) * 100,
    ),
    toPayToPaid: Math.round(
        (salesFunnelData[3].value / salesFunnelData[2].value) * 100,
    ),
    overall: Math.round((salesFunnelData[3].value / salesFunnelData[0].value) * 100),
};

// Calculate revenue by sales agent
export const revenueBySalesAgent = mockSalesAgents
    .map((agent) => {
        const agentOrders = mockOrders.filter(
            (order) =>
                order.salesAgent?.uid === agent.uid && order.stage === OrderStage.PAID,
        );

        const revenue = agentOrders.reduce((sum, order) => {
            return (
                sum +
                (order.agreedTotal ||
                    order.orderItems.reduce(
                        (itemSum, item) => itemSum + item.totalPrice,
                        0,
                    ))
            );
        }, 0);

        return {
            name: agent.fullName || `${agent.firstName} ${agent.lastName}`,
            value: revenue,
        };
    })
    .sort((a, b) => b.value - a.value);

// Top selling products
export const topSellingProducts = mockOfferings
    .map((offering) => {
        const count = mockOrders.filter(
            (order) =>
                order.stage === OrderStage.PAID &&
                order.orderItems.some((item) => item.offering.oid === offering.oid),
        ).length;

        const revenue = mockOrders
            .filter((order) => order.stage === OrderStage.PAID)
            .reduce((sum, order) => {
                const offeringItems = order.orderItems.filter(
                    (item) => item.offering.oid === offering.oid,
                );
                return (
                    sum +
                    offeringItems.reduce(
                        (itemSum, item) => itemSum + item.totalPrice,
                        0,
                    )
                );
            }, 0);

        return {
            name: offering.name,
            count,
            revenue,
        };
    })
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

// Recent orders
export const recentOrders = [...mockOrders]
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 10);

// Sales performance data
export const currentMonthPerformance = {
    period: "Mayo 2025",
    sales: totalRevenue * 0.15, // 15% of total revenue
    orders: mockOrders.filter((order) => order.createdAt.startsWith("2025-05")).length,
    conversion: 23.5,
    previousSales: totalRevenue * 0.14,
    previousOrders: 45,
    previousConversion: 21.8,
};
