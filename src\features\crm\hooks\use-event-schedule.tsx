import { useMutation, useQuery } from "@tanstack/react-query";
import {
    CreateEventScheduleFormBody,
    ListEventSchedulesQueryParams,
} from "@/features/crm/types/event-schedule";
import {
    createEventSchedule,
    listEventSchedules,
} from "@/features/crm/services/portals/event-schedule";

type UseEventSchedulesProps = {
    queryParams?: ListEventSchedulesQueryParams;
};

export const useEventSchedules = ({ queryParams }: UseEventSchedulesProps = {}) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["event-schedules", queryParams],
        queryFn: () => listEventSchedules(queryParams),
        refetchOnWindowFocus: false,
    });

    const { count, results: eventSchedules } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        eventSchedules,
        count,
    };
};

type UseCreateEventScheduleProps = {
    onSuccess?: () => void;
    onError?: () => void;
};

export const useCreateEventSchedule = ({
    onSuccess,
    onError,
}: UseCreateEventScheduleProps = {}) => {
    return useMutation({
        mutationFn: (newEventSchedule: CreateEventScheduleFormBody) =>
            createEventSchedule(newEventSchedule),
        onSuccess,
        onError,
    });
};
