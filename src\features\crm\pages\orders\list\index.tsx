import { useMemo, useState } from "react";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import CrmLayout from "@/features/crm/layout";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    DatePicker,
    Drawer,
    Input,
    Modal,
    Pagination,
    Radio,
    Space,
    Switch,
    Tag,
    Tooltip,
    Typography,
} from "antd";
import { Columns3, Eye, Plus, Rows3, SlidersHorizontal } from "lucide-react";
import CreateOrderForm from "@/features/crm/components/organisms/create-order-form";
import { useOrders } from "@/features/crm/hooks/use-order";
import { CreateOrderFormValues, Order, OrderStage } from "@/features/crm/types/order";
import { useSearchParams } from "react-router-dom";
import OrdersTable from "@/features/crm/components/organisms/orders-table";
import OrdersKanbanView from "@/features/crm/components/organisms/orders-kanban-view";
import { ListOrdersQueryParams } from "@/features/crm/services/portals/order";

const { Text } = Typography;
const { Search } = Input;

enum DisplayMode {
    LIST = "list",
    KANBAN = "kanban",
}

const getStageColor = (stage: OrderStage) => {
    switch (stage) {
        case OrderStage.PROSPECT:
            return "blue";
        case OrderStage.INTERESTED:
            return "green";
        case OrderStage.TO_PAY:
            return "orange";
        case OrderStage.PAID:
            return "purple";
        case OrderStage.LOST:
            return "red";
        default:
            return "default";
    }
};

const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE = 1;

export default function OrdersListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const display = searchParams.get("display") || DisplayMode.KANBAN;
    const showLostColumn = searchParams.get("showLostColumn") === "true" || false;
    const searchQuery = searchParams.get("search") || "";
    const page = parseInt(searchParams.get("page") || "1", 10) || DEFAULT_PAGE;
    const pageSize =
        parseInt(searchParams.get("pageSize") || DEFAULT_PAGE_SIZE.toString(), 10) ||
        DEFAULT_PAGE_SIZE;

    const [createOrderModalOpen, setCreateOrderModalOpen] = useState<boolean>(false);
    const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState<boolean>(false);
    const [createOrderInitialValues, setCreateOrderInitialValues] = useState<
        Partial<CreateOrderFormValues>
    >({});

    const queryParams: ListOrdersQueryParams = {
        page,
        pageSize,
        search: searchQuery,
    };

    const { orders, count } = useOrders(queryParams);

    const handleCloseCreateOrderModal = () => {
        setCreateOrderInitialValues({});
        setCreateOrderModalOpen(false);
    };

    const handleOpenCreateOrderModal = (stage: OrderStage) => {
        setCreateOrderInitialValues({ stage });
        setCreateOrderModalOpen(true);
    };

    const prospectOrders: Order[] = useMemo(
        () => orders.filter((order) => order.stage === OrderStage.PROSPECT),
        [orders],
    );
    const interestedOrders: Order[] = useMemo(
        () => orders.filter((order) => order.stage === OrderStage.INTERESTED),
        [orders],
    );
    const toPayOrders: Order[] = useMemo(
        () => orders.filter((order) => order.stage === OrderStage.TO_PAY),
        [orders],
    );
    const paidOrders: Order[] = useMemo(
        () => orders.filter((order) => order.stage === OrderStage.PAID),
        [orders],
    );
    const lostOrders: Order[] = useMemo(
        () => orders.filter((order) => order.stage === OrderStage.LOST),
        [orders],
    );

    const handleDisplayChange = (display: DisplayMode) => {
        setSearchParams((prev) => {
            prev.set("display", display);
            return prev;
        });
    };

    const handleShowLostColumnChange = (showLostColumn: boolean) => {
        setSearchParams((prev) => {
            prev.set("showLostColumn", showLostColumn.toString());
            return prev;
        });
    };

    const handleSetPage = (page: number, pageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", page.toString());
            prev.set("pageSize", pageSize.toString());
            return prev;
        });
    };

    return (
        <CrmLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí las órdenes de venta de los clientes de CEU" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Plus />}
                            onClick={() => {
                                setCreateOrderModalOpen(true);
                            }}
                        >
                            Agregar
                        </Button>
                        <Modal
                            centered
                            open={createOrderModalOpen}
                            onCancel={() => {
                                setCreateOrderInitialValues({});
                                setCreateOrderModalOpen(false);
                            }}
                            footer={false}
                            title={
                                <div className="w-full flex justify-center text-xl py-1">
                                    Agregar nueva Orden
                                </div>
                            }
                        >
                            <CreateOrderForm
                                handleCloseModal={handleCloseCreateOrderModal}
                                initialValues={createOrderInitialValues}
                            />
                        </Modal>
                    </div>
                </div>
                <div className="p-5 bg-white-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center">
                        <Text className="text-black-medium text-2xl font-semibold">
                            Órdenes <Badge count={count} color="blue" size="default" />
                        </Text>
                        <Search
                            size="large"
                            placeholder="Buscar por nombre, email o teléfono"
                            onSearch={(value) => {
                                setSearchParams((prev) => {
                                    if (value) {
                                        prev.set("search", value);
                                    } else {
                                        prev.delete("search");
                                    }
                                    return prev;
                                });
                            }}
                            defaultValue={searchQuery}
                            enterButton
                            allowClear
                            className="max-w-screen-sm"
                        />
                        <div className="flex items-center gap-3">
                            <Radio.Group
                                value={display}
                                onChange={(e) => handleDisplayChange(e.target.value)}
                                style={{ display: "flex" }}
                            >
                                <Radio.Button
                                    value={DisplayMode.LIST}
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        marginRight: "-1px",
                                    }}
                                >
                                    <Rows3 size={16} />
                                </Radio.Button>
                                <Radio.Button
                                    value={DisplayMode.KANBAN}
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                >
                                    <Columns3 size={16} />
                                </Radio.Button>
                            </Radio.Group>

                            <Tooltip title="Mostrar columna de perdidos">
                                <div className="flex items-center gap-2">
                                    <Eye size={16} />
                                    <Switch
                                        checked={showLostColumn}
                                        onChange={(checked) =>
                                            handleShowLostColumnChange(checked)
                                        }
                                    />
                                </div>
                            </Tooltip>

                            <Button
                                icon={<SlidersHorizontal size={16} />}
                                onClick={() => setIsFilterDrawerOpen(true)}
                            >
                                Filtros
                            </Button>
                            <Drawer
                                title="Aplicar filtros"
                                placement="right"
                                closable={true}
                                onClose={() => setIsFilterDrawerOpen(false)}
                                open={isFilterDrawerOpen}
                                width={320}
                            >
                                <div className="space-y-4">
                                    <div>
                                        <h4 className="font-medium mb-2">Etapas</h4>
                                        <div className="space-y-2">
                                            {Object.values(OrderStage).map((stage) => (
                                                <div
                                                    key={stage}
                                                    className="flex items-center"
                                                >
                                                    <Switch
                                                        defaultChecked
                                                        size="small"
                                                        className="mr-2"
                                                    />
                                                    <Tag
                                                        color={getStageColor(
                                                            stage as OrderStage,
                                                        )}
                                                    >
                                                        {stage.charAt(0).toUpperCase() +
                                                            stage
                                                                .slice(1)
                                                                .toLowerCase()}
                                                    </Tag>
                                                </div>
                                            ))}
                                        </div>
                                    </div>

                                    <div>
                                        <h4 className="font-medium mb-2">Moneda</h4>
                                        <Radio.Group defaultValue="all">
                                            <Space direction="vertical">
                                                <Radio value="all">Todas</Radio>
                                                <Radio value="usd">Dólares (USD)</Radio>
                                                <Radio value="pen">Soles (PEN)</Radio>
                                            </Space>
                                        </Radio.Group>
                                    </div>

                                    <div>
                                        <h4 className="font-medium mb-2">
                                            Rango de fecha
                                        </h4>
                                        <DatePicker.RangePicker
                                            style={{ width: "100%" }}
                                            placeholder={["Fecha inicio", "Fecha fin"]}
                                        />
                                    </div>

                                    <div className="pt-4">
                                        <Button type="primary" block>
                                            Aplicar filtros
                                        </Button>
                                    </div>
                                </div>
                            </Drawer>
                        </div>
                    </div>
                </div>
                {display === DisplayMode.KANBAN ? (
                    <OrdersKanbanView
                        showLostColumn={showLostColumn}
                        prospectOrders={prospectOrders}
                        interestedOrders={interestedOrders}
                        toPayOrders={toPayOrders}
                        paidOrders={paidOrders}
                        lostOrders={lostOrders}
                        handleOpenCreateOrderModal={handleOpenCreateOrderModal}
                    />
                ) : (
                    <OrdersTable orders={orders} />
                )}

                <div className="flex justify-between items-center p-5 bg-white-full rounded-lg shadow-sm">
                    <Text type="secondary">
                        {orders.length} de {count} órdenes
                    </Text>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={count}
                        onChange={handleSetPage}
                        showSizeChanger
                    />
                </div>
            </div>
        </CrmLayout>
    );
}
