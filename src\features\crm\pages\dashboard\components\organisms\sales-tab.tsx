import { useState, useMemo } from "react";
import { Row, Col, Card, Statistic } from "antd";
import {
    DollarSign,
    Users,
    BarChart2,
    Activity,
    TrendingUp,
    Percent,
    <PERSON><PERSON><PERSON>,
    Pie<PERSON>hart,
    FilterX,
} from "lucide-react";

// Components
import StatCard from "@/features/crm/components/atoms/stat-card";
import PieChartCard from "@/features/crm/components/molecules/pie-chart-card";
import BarChartCard from "@/features/crm/components/molecules/bar-chart-card";
import FunnelChartCard from "@/features/crm/components/molecules/funnel-chart-card";
import OrderFilterCard from "@/features/crm/components/molecules/order-filter-card";
import TopProductsCard from "@/features/crm/components/molecules/top-products-card";
import SalesPerformanceCard from "@/features/crm/components/molecules/sales-performance-card";
import OrdersTable from "@/features/crm/components/organisms/orders-table";

// Mock data
import {
    mockOrders,
    ordersByStage,
    salesFunnelData,
    ordersByMonth,
    revenueByMonth,
    totalRevenue,
    conversionRates,
    revenueBySalesAgent,
    topSellingProducts,
    recentOrders,
    currentMonthPerformance,
} from "@/features/crm/mock/order-data";
import { OrderStage } from "@/features/crm/types/order";

export default function SalesDashboardTab() {
    const [filters, setFilters] = useState({});
    console.log("Filters:", filters);

    // Filtramos las órdenes en base a los filtros seleccionados
    const filteredOrders = useMemo(() => {
        return recentOrders;
        // En un caso real, aquí filtraríamos las órdenes según los filtros
    }, []);

    // Calcular KPIs
    const prospectCount = mockOrders.filter(
        (o) => o.stage === OrderStage.PROSPECT,
    ).length;
    const interestedCount = mockOrders.filter(
        (o) => o.stage === OrderStage.INTERESTED,
    ).length;
    const toPayCount = mockOrders.filter((o) => o.stage === OrderStage.TO_PAY).length;
    const paidCount = mockOrders.filter((o) => o.stage === OrderStage.PAID).length;
    const lostCount = mockOrders.filter((o) => o.stage === OrderStage.LOST).length;

    // Colores para los gráficos
    const stageColors = ["#4096ff", "#36cfc9", "#faad14", "#73d13d", "#ff4d4f"];
    const funnelColors = ["#4096ff", "#36cfc9", "#faad14", "#73d13d"];

    return (
        <div>
            {/* Filtros */}
            <OrderFilterCard onFilterChange={setFilters} />

            {/* Stats Cards */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} sm={12} lg={8} xl={6}>
                    <StatCard
                        title="Total en ventas"
                        value={`S/. ${totalRevenue.toLocaleString()}`}
                        icon={<DollarSign size={24} className="text-green-500" />}
                        color="#73d13d"
                    />
                </Col>
                <Col xs={24} sm={12} lg={8} xl={6}>
                    <StatCard
                        title="Clientes"
                        value={`${mockOrders.length}`}
                        icon={<Users size={24} className="text-blue-500" />}
                        color="#4096ff"
                    />
                </Col>
                <Col xs={24} sm={12} lg={8} xl={6}>
                    <StatCard
                        title="Tasa de conversión"
                        value={`${conversionRates.overall}%`}
                        icon={<Percent size={24} className="text-purple-500" />}
                        color="#722ed1"
                        suffix={<span className="text-xs text-green-500">+2.5%</span>}
                    />
                </Col>
                <Col xs={24} sm={12} lg={8} xl={6}>
                    <StatCard
                        title="Ventas este mes"
                        value={`${paidCount}`}
                        icon={<TrendingUp size={24} className="text-orange-500" />}
                        color="#fa8c16"
                        suffix={<span className="text-xs text-green-500">+12%</span>}
                    />
                </Col>
            </Row>

            {/* Conversion Funnel */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={12}>
                    <FunnelChartCard
                        title="Embudo de Conversión"
                        data={salesFunnelData}
                        colors={funnelColors}
                        icon={<FilterX className="h-5 w-5 text-blue-500" />}
                        showConversionRates={true}
                    />
                </Col>
                <Col xs={24} lg={12}>
                    <Card
                        title={
                            <div className="flex items-center">
                                <LineChart className="mr-2 h-5 w-5 text-green-500" />
                                <span>Conversión entre etapas</span>
                            </div>
                        }
                    >
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Card className="bg-gray-50">
                                <Statistic
                                    title="Prospecto → Interesado"
                                    value={conversionRates.prospectToInterested}
                                    suffix="%"
                                    precision={1}
                                    valueStyle={{ color: "#3f8600" }}
                                />
                                <div className="text-xs text-gray-500 mt-2">
                                    {interestedCount} de {prospectCount} prospectos
                                </div>
                            </Card>
                            <Card className="bg-gray-50">
                                <Statistic
                                    title="Interesado → Por Pagar"
                                    value={conversionRates.interestedToPay}
                                    suffix="%"
                                    precision={1}
                                    valueStyle={{ color: "#3f8600" }}
                                />
                                <div className="text-xs text-gray-500 mt-2">
                                    {toPayCount} de {interestedCount} interesados
                                </div>
                            </Card>
                            <Card className="bg-gray-50">
                                <Statistic
                                    title="Por Pagar → Pagado"
                                    value={conversionRates.toPayToPaid}
                                    suffix="%"
                                    precision={1}
                                    valueStyle={{ color: "#3f8600" }}
                                />
                                <div className="text-xs text-gray-500 mt-2">
                                    {paidCount} de {toPayCount} por pagar
                                </div>
                            </Card>
                        </div>
                        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                            <div className="font-medium text-blue-800 mb-2">
                                Resumen
                            </div>
                            <div className="flex justify-between">
                                <div className="text-blue-700">
                                    <div className="text-sm">
                                        Tasa de conversión general
                                    </div>
                                    <div className="text-xl font-bold">
                                        {conversionRates.overall}%
                                    </div>
                                </div>
                                <div className="text-blue-700">
                                    <div className="text-sm">Pérdidas</div>
                                    <div className="text-xl font-bold">
                                        {lostCount} órdenes
                                    </div>
                                </div>
                                <div className="text-green-700">
                                    <div className="text-sm">Ventas cerradas</div>
                                    <div className="text-xl font-bold">
                                        {paidCount} órdenes
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Card>
                </Col>
            </Row>

            {/* Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={16}>
                    <BarChartCard
                        title="Órdenes por mes"
                        data={ordersByMonth}
                        dataKey="total"
                        barColor="#4096ff"
                        icon={<BarChart2 className="h-5 w-5 text-blue-500" />}
                    />
                </Col>
                <Col xs={24} lg={8}>
                    <PieChartCard
                        title="Estado de las órdenes"
                        data={ordersByStage}
                        colors={stageColors}
                        icon={<Activity className="h-5 w-5 text-yellow-500" />}
                    />
                </Col>
            </Row>

            {/* Detailed Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={12}>
                    <BarChartCard
                        title="Ingresos por mes"
                        data={revenueByMonth}
                        dataKey="revenue"
                        barColor="#73d13d"
                        icon={<DollarSign className="h-5 w-5 text-green-500" />}
                        formatter={(value) => `S/. ${value.toLocaleString()}`}
                    />
                </Col>
                <Col xs={24} lg={12}>
                    <TopProductsCard
                        title="Top Productos"
                        data={topSellingProducts}
                        icon={<PieChart className="h-5 w-5 text-blue-500" />}
                    />
                </Col>
            </Row>

            {/* Performance Card */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24}>
                    <SalesPerformanceCard
                        title="Rendimiento Mensual"
                        data={currentMonthPerformance}
                        icon={<TrendingUp className="h-5 w-5 text-blue-500" />}
                    />
                </Col>
            </Row>

            {/* Revenue by Sales Agent */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24}>
                    <Card
                        title={
                            <div className="flex items-center">
                                <Users className="mr-2 h-5 w-5 text-purple-500" />
                                <span>Ventas por agente comercial</span>
                            </div>
                        }
                    >
                        <Row gutter={[16, 16]}>
                            {revenueBySalesAgent.map((agent, index) => (
                                <Col xs={24} sm={12} md={8} lg={6} key={index}>
                                    <Card className="bg-gray-50">
                                        <Statistic
                                            title={agent.name}
                                            value={agent.value}
                                            precision={0}
                                            valueStyle={{ color: "#3f8600" }}
                                            prefix="S/. "
                                            formatter={(value) =>
                                                `${value.toLocaleString()}`
                                            }
                                        />
                                        <div className="text-xs text-gray-500 mt-2">
                                            {Math.round(
                                                (agent.value / totalRevenue) * 100,
                                            )}
                                            % del total
                                        </div>
                                    </Card>
                                </Col>
                            ))}
                        </Row>
                    </Card>
                </Col>
            </Row>

            {/* Recent Orders Table */}
            <Row gutter={[16, 16]}>
                <Col xs={24}>
                    <Card
                        title={
                            <div className="flex items-center">
                                <Activity className="mr-2 h-5 w-5 text-blue-500" />
                                <span>Órdenes recientes</span>
                            </div>
                        }
                        className="mb-6"
                    >
                        <OrdersTable orders={filteredOrders} />
                    </Card>
                </Col>
            </Row>
        </div>
    );
}
