import { PaymentCurrency, PaymentListItem, PaymentOrder } from "../types/payment";
import { OrderStage } from "../types/order";

// Generate random dates within the last 6 months
const getRandomDate = (months = 6) => {
    const now = new Date();
    const pastDate = new Date(now);
    pastDate.setMonth(now.getMonth() - Math.floor(Math.random() * months));
    pastDate.setDate(Math.floor(Math.random() * 28) + 1); // Random day between 1-28
    return pastDate.toISOString();
};

// Mock payment methods
export const paymentMethods = [
    {
        pmid: "pm_1",
        key: "pm_1",
        name: "Tarjeta de Crédito",
        created_at: getRandomDate(),
        updated_at: getRandomDate(),
        description: null,
    },
    {
        pmid: "pm_2",
        key: "pm_2",
        name: "Transferencia Bancaria",
        created_at: getRandomDate(),
        updated_at: getRandomDate(),
        description: null,
    },
    {
        pmid: "pm_3",
        key: "pm_3",
        name: "Ya<PERSON>/<PERSON><PERSON>",
        created_at: getRandomDate(),
        updated_at: getRandomDate(),
        description: null,
    },
    {
        pmid: "pm_4",
        key: "pm_4",
        name: "PayPal",
        created_at: getRandomDate(),
        updated_at: getRandomDate(),
        description: null,
    },
    {
        pmid: "pm_5",
        key: "pm_5",
        name: "Depósito en Efectivo",
        created_at: getRandomDate(),
        updated_at: getRandomDate(),
        description: null,
    },
];

// Generate sample orders with different stages
const generateSampleOrders = (): PaymentOrder[] => {
    const stages = Object.values(OrderStage);
    const names = [
        "Juan Pérez",
        "María López",
        "Carlos Gómez",
        "Ana Torres",
        "Ricardo Díaz",
        "Sofía Mendoza",
        "Daniel Vargas",
        "Lucía Herrera",
        "Eduardo Castro",
        "Carmen Ortega",
    ];

    return Array.from({ length: 10 }).map((_, index) => ({
        oid: `order_${Math.random().toString(36).substring(2, 10)}`,
        stage: stages[Math.floor(Math.random() * stages.length)],
        owner: {
            uid: `user_${index + 1}`,
            fullName: names[index],
            email: `${names[index].toLowerCase().replace(" ", ".")}@ejemplo.com`,
            phoneNumber: index % 2 === 0 ? "+51999888777" : "+1234567890", // Alternating between PEN and USD
        },
    }));
};

const sampleOrders = generateSampleOrders();

// Generate mock payments data
export const mockPayments: PaymentListItem[] = Array.from({ length: 50 }).map(
    (_, index) => {
        const isPaid = Math.random() > 0.3;
        const orderIndex = Math.floor(Math.random() * sampleOrders.length);
        const order = sampleOrders[orderIndex];
        const createdDate = getRandomDate();
        const updatedDate = new Date(createdDate);
        updatedDate.setDate(updatedDate.getDate() + Math.floor(Math.random() * 10));

        return {
            key: `payment_${index}`,
            pid: `pid_${Math.random().toString(36).substring(2, 10)}`,
            order,
            paymentMethod:
                paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
            paymentDate: getRandomDate(3),
            amount: Math.floor(Math.random() * 5000) + 500, // Amount between 500 and 5500
            currency: order.owner.phoneNumber.startsWith("+51")
                ? PaymentCurrency.PEN
                : PaymentCurrency.USD,
            isPaid,
            createdAt: createdDate,
            updatedAt: updatedDate.toISOString(),
        };
    },
);

// Get payments by status (paid/pending)
export const paidPayments = mockPayments.filter((payment) => payment.isPaid);
export const pendingPayments = mockPayments.filter((payment) => !payment.isPaid);

// Get total amounts
export const totalPaidPEN = paidPayments
    .filter((p) => p.currency === PaymentCurrency.PEN)
    .reduce((sum, payment) => sum + payment.amount, 0);

export const totalPaidUSD = paidPayments
    .filter((p) => p.currency === PaymentCurrency.USD)
    .reduce((sum, payment) => sum + payment.amount, 0);

export const totalPendingPEN = pendingPayments
    .filter((p) => p.currency === PaymentCurrency.PEN)
    .reduce((sum, payment) => sum + payment.amount, 0);

export const totalPendingUSD = pendingPayments
    .filter((p) => p.currency === PaymentCurrency.USD)
    .reduce((sum, payment) => sum + payment.amount, 0);

// Payments by month
const getMonthName = (date: Date) => {
    return new Intl.DateTimeFormat("es", { month: "long" }).format(date);
};

export const getPaymentsByMonth = () => {
    const monthsMap: Record<string, { month: string; paid: number; pending: number }> =
        {};

    // Initialize the last 6 months
    for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthName = getMonthName(date);
        monthsMap[monthName] = { month: monthName, paid: 0, pending: 0 };
    }

    // Fill with data
    mockPayments.forEach((payment) => {
        const paymentDate = new Date(payment.paymentDate);
        const month = getMonthName(paymentDate);

        if (monthsMap[month]) {
            if (payment.isPaid) {
                monthsMap[month].paid += payment.amount;
            } else {
                monthsMap[month].pending += payment.amount;
            }
        }
    });

    return Object.values(monthsMap);
};

export const paymentsByMonth = getPaymentsByMonth();

// Payments by method
export const getPaymentsByMethod = () => {
    const methodsMap = new Map<
        string,
        { name: string; value: number; count: number }
    >();

    mockPayments.forEach((payment) => {
        if (!methodsMap.has(payment.paymentMethod.pmid)) {
            methodsMap.set(payment.paymentMethod.pmid, {
                name: payment.paymentMethod.name,
                value: 0,
                count: 0,
            });
        }

        const methodData = methodsMap.get(payment.paymentMethod.pmid)!;
        methodData.value += payment.amount;
        methodData.count += 1;
    });

    return Array.from(methodsMap.values());
};

export const paymentsByMethod = getPaymentsByMethod();

// Payment stage distribution
export const getPaymentsByStage = () => {
    const stagesMap = new Map<string, { name: string; value: number; count: number }>();

    mockPayments.forEach((payment) => {
        const stage = payment.order.stage;
        if (!stagesMap.has(stage)) {
            stagesMap.set(stage, {
                name: stageToLabel[stage],
                value: 0,
                count: 0,
            });
        }

        const stageData = stagesMap.get(stage)!;
        stageData.value += payment.amount;
        stageData.count += 1;
    });

    return Array.from(stagesMap.values());
};

const stageToLabel: Record<OrderStage, string> = {
    [OrderStage.PROSPECT]: "Prospecto",
    [OrderStage.INTERESTED]: "Interesado",
    [OrderStage.TO_PAY]: "Por pagar",
    [OrderStage.SOLD]: "Vendido",
    [OrderStage.LOST]: "Perdido",
};

export const paymentsByStage = getPaymentsByStage();

// Payment efficiency (time from creation to payment)
export const paymentEfficiency = mockPayments
    .filter((p) => p.isPaid)
    .map((payment) => {
        const createdDate = new Date(payment.createdAt);
        const paidDate = new Date(payment.paymentDate);
        const daysDiff =
            (paidDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24);

        return {
            pid: payment.pid,
            customerName: payment.order.owner.fullName,
            createdAt: payment.createdAt,
            paymentDate: payment.paymentDate,
            daysToPay: Math.max(0, Math.round(daysDiff)),
            amount: payment.amount,
            currency: payment.currency,
        };
    })
    .sort((a, b) => a.daysToPay - b.daysToPay)
    .slice(0, 10);

// Recent payments
export const recentPayments = [...mockPayments]
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 10);

// Top customers by payment amount
export const getTopCustomers = () => {
    const customersMap = new Map<
        string,
        {
            uid: string;
            name: string;
            totalAmount: number;
            paymentsCount: number;
        }
    >();

    mockPayments.forEach((payment) => {
        const uid = payment.order.owner.uid;
        if (!customersMap.has(uid)) {
            customersMap.set(uid, {
                uid,
                name: payment.order.owner.fullName,
                totalAmount: 0,
                paymentsCount: 0,
            });
        }

        const customerData = customersMap.get(uid)!;
        customerData.totalAmount += payment.amount;
        customerData.paymentsCount += 1;
    });

    return Array.from(customersMap.values())
        .sort((a, b) => b.totalAmount - a.totalAmount)
        .slice(0, 5);
};

export const topCustomers = getTopCustomers();

// Conversion metrics
export const paymentConversionMetrics = {
    totalToPayOrders: mockPayments.filter((p) => p.order.stage === OrderStage.TO_PAY)
        .length,
    convertedToPayOrders: mockPayments.filter(
        (p) => p.order.stage === OrderStage.TO_PAY && p.isPaid,
    ).length,
    conversionRate: 65.2, // Calculated percentage
    averageDaysToPay: 3.7, // Average days from order to payment
    paymentRecoveryRate: 78.5, // Percentage of payments recovered after follow-up
};

// Currency distribution
export const paymentsByCurrency = [
    {
        name: "Soles (PEN)",
        value: mockPayments.filter((p) => p.currency === PaymentCurrency.PEN).length,
    },
    {
        name: "Dólares (USD)",
        value: mockPayments.filter((p) => p.currency === PaymentCurrency.USD).length,
    },
];
