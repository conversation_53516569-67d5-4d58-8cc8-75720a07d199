import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import {
    PartnershipCreate,
    PartnershipRetrieve,
    PartnershipListItem,
} from "@/features/crm/types/partnership";

export const listPartnerships = async (): Promise<
    PaginatedResponse<PartnershipListItem>
> => {
    const res = await portalsApi.get("crm/partnerships");
    return res.data;
};

export const createPartnership = async (
    partnership: PartnershipCreate,
): Promise<PartnershipRetrieve> => {
    const res = await portalsApi.post("crm/partnerships", partnership);
    return res.data;
};
