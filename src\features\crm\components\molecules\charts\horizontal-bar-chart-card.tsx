import React from "react";
import { Card } from "antd";
import {
    ResponsiveContainer,
    BarChart,
    Bar,
    XAxis,
    YAxis,
    Cell,
    Tooltip,
} from "recharts";

interface HorizontalBarChartCardProps {
    title: string;
    data: Array<Record<string, unknown>>;
    valueKey?: string;
    categoryKey?: string;
    colors?: string[];
    icon?: React.ReactNode;
    formatter?: (value: number) => string;
    height?: number;
}

const getColor = (length: number, index: number, colors: string[]) => {
    if (length <= colors.length) {
        return colors[index];
    }
    return colors[index % colors.length];
};

const HorizontalBarChartCard: React.FC<HorizontalBarChartCardProps> = ({
    title,
    data,
    valueKey = "value",
    categoryKey = "name",
    colors = ["#1890ff", "#faad14"],
    icon,
    formatter,
    height,
}) => {
    // Calcular la altura dinámica basada en la cantidad de elementos
    const chartHeight = height || Math.max(50 * data.length, 200);

    // Componente personalizado para el tooltip
    interface CustomTooltipProps {
        active?: boolean;
        payload?: Array<{
            value: number;
            dataKey: string;
            name: string;
            color: string;
        }>;
        label?: string;
    }

    const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const { value, color } = payload[0];
            return (
                <div className="bg-white-full p-2 border border-gray-200 shadow-md rounded">
                    <p className="font-medium">Cantidad</p>
                    <p style={{ color }}>{formatter ? formatter(value) : value}</p>
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <div style={{ height: chartHeight, width: "100%" }}>
                <ResponsiveContainer width="100%" height="100%" debounce={50}>
                    <BarChart
                        data={data}
                        layout="vertical"
                        margin={{
                            left: 36,
                            top: 10,
                            bottom: 10,
                        }}
                    >
                        <XAxis hide axisLine={false} type="number" />
                        <YAxis
                            yAxisId={0}
                            dataKey={categoryKey}
                            type="category"
                            axisLine={false}
                            tickLine={false}
                            
                        />
                        <YAxis
                            orientation="right"
                            yAxisId={1}
                            dataKey={valueKey}
                            type="category"
                            axisLine={false}
                            tickLine={false}
                            tickFormatter={formatter}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Bar dataKey={valueKey} minPointSize={2}>
                            {data.map((_, idx) => (
                                <Cell
                                    key={`cell-${idx}`}
                                    fill={getColor(data.length, idx, colors)}
                                />
                            ))}
                        </Bar>
                    </BarChart>
                </ResponsiveContainer>
            </div>
        </Card>
    );
};

export default HorizontalBarChartCard;
