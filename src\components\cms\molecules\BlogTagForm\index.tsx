import { Form, Input, ColorPicker, Divider, Row, Col } from "antd";
import type { FormProps, ColorPickerProps } from "antd";
import { BlogTag, CreateBlogTagBody, UpdateBlogTagBody } from "@myTypes/blog";
import { Color } from "antd/es/color-picker";

const colorPresets: ColorPickerProps["presets"] = [
    {
        label: "Azul",
        colors: [
            "#01050F",
            "#020B22",
            "#03153F",
            "#052161",
            "#062A7E",
            "#0836A0",
            "#0B4BDF",
            "#3D74F5",
            "#7CA1F8",
            "#C0D2FC",
            "#DDE7FD",
        ],
    },
    {
        label: "Verde",
        colors: [
            "#131201",
            "#262403",
            "#474405",
            "#6D6808",
            "#8F880A",
            "#B4AD0D",
            "#EEE311",
            "#F2EA4F",
            "#F6F188",
            "#FBF8C6",
            "#FDFCE2",
        ],
    },
    {
        label: "Naranja",
        colors: [
            "#190F00",
            "#382200",
            "#6B4000",
            "#A36200",
            "#D68100",
            "#FF9F0F",
            "#FFB13D",
            "#FFC670",
            "#FFD89E",
            "#FFEDD1",
            "#FFF5E5",
        ],
    },
];

const customPanelRender: ColorPickerProps["panelRender"] = (
    _,
    { components: { Picker, Presets } },
) => (
    <Row justify="space-between" wrap={false}>
        <Col span={12}>
            <Presets />
        </Col>
        <Divider type="vertical" style={{ height: "auto" }} />
        <Col flex="auto">
            <Picker />
        </Col>
    </Row>
);

interface BlogTagFormProps {
    initialValues?: Partial<BlogTag>;
    onFinish: FormProps<CreateBlogTagBody | UpdateBlogTagBody>["onFinish"];
    form: FormProps<CreateBlogTagBody | UpdateBlogTagBody>["form"];
}

export default function BlogTagForm({
    initialValues,
    onFinish,
    form,
}: BlogTagFormProps) {
    return (
        <Form
            name="blogTag"
            layout="vertical"
            form={form}
            initialValues={initialValues}
            onFinish={onFinish}
        >
            <div className="grid grid-cols-1 gap-4">
                <Form.Item
                    name="name"
                    label={<span className="font-semibold">Nombre</span>}
                    rules={[
                        {
                            required: true,
                            message: "Por favor ingrese el nombre de la etiqueta",
                        },
                    ]}
                >
                    <Input placeholder="Ej. JavaScript" className="py-1" />
                </Form.Item>

                <Form.Item
                    name="slug"
                    label={<span className="font-semibold">Slug</span>}
                    rules={[
                        {
                            required: true,
                            message: "Por favor ingrese el slug de la etiqueta",
                        },
                    ]}
                >
                    <Input placeholder="Ej. javascript" className="py-1" />
                </Form.Item>

                <Form.Item
                    name="description"
                    label={<span className="font-semibold">Descripción</span>}
                >
                    <Input.TextArea
                        placeholder="Descripción de la etiqueta"
                        rows={3}
                        className="py-1"
                    />
                </Form.Item>

                <Form.Item
                    name="badgeColor"
                    label={<span className="font-semibold">Color de la etiqueta</span>}
                    rules={[
                        {
                            required: true,
                            message: "Por favor seleccione un color para la etiqueta",
                        },
                    ]}
                >
                    <ColorPicker
                        showText
                        format="hex"
                        defaultValue="#1677ff"
                        presets={colorPresets}
                        panelRender={customPanelRender}
                        styles={{ popupOverlayInner: { width: 480 } }}
                        onChange={(color: Color) => {
                            form?.setFieldsValue({ badgeColor: color.toHexString() });
                        }}
                    />
                </Form.Item>
            </div>
        </Form>
    );
}
