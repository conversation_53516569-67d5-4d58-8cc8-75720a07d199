import { Select, SelectProps } from "antd";
import { ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";
import { useOfferings } from "../../hooks/use-offering";

interface SelectOfferingProps extends Omit<SelectProps, "options"> {
    value?: string;
    onChange?: (value: string) => void;
}

export default function SelectOffering({
    value,
    onChange,
    ...restProps
}: SelectOfferingProps) {
    const { offerings, isLoading } = useOfferings();

    const offeringOptions: SelectProps["options"] =
        offerings.map((offering) => ({
            value: offering.oid,
            label: offering.name,
            data: {
                info: offering,
            },
        })) || [];

    return (
        <Select
            {...restProps}
            value={value}
            onChange={onChange}
            options={offeringOptions}
            optionRender={(option) => (
                <div className="flex justify-between items-center">
                    <span>{option.data.label}</span>
                    <span className="text-xs text-gray-600">
                        {option.data.data.info.slug}
                    </span>
                    <Link to={`/crm/offerings/${option.data.value}`} title="Ver oferta">
                        <ExternalLink size={14} />
                    </Link>
                </div>
            )}
            loading={isLoading}
            optionFilterProp="label"
            allowClear
            showSearch
        />
    );
}
