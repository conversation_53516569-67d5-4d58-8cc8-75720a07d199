import { useMemo, useState } from "react";
import {
    Button,
    Checkbox,
    ConfigProvider,
    DatePicker,
    Dropdown,
    Empty,
    Form,
    Input,
    Modal,
    Pagination,
    Popover,
    Select,
    Table,
    Tag,
    Typography,
} from "antd";
import type { TableProps } from "antd";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Link, useNavigate, useSearchParams } from "react-router-dom";

const { Text } = Typography;
const { Search } = Input;
const { RangePicker } = DatePicker;

import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import Spinner from "@components/shared/atoms/Spinner";
import { createOffering, listOffering } from "@services/portals/cms/offering";

import Import from "@assets/icons/huge/import.svg?react";
import Plus from "@assets/icons/general/plus-white.svg?react";
import Reload from "@assets/icons/huge/reload.svg?react";
import Settings from "@assets/icons/huge/settings.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import MoreVertical from "@assets/icons/huge/more-vertical.svg?react";

import { formatDate, formatDateTime } from "@lib/helpers";
import {
    CreateOfferingBody,
    CreateOfferingFormValues,
    Offering,
    OfferingFormat,
    OfferingFormatLabel,
    OfferingModality,
    OfferingModalityLabel,
    OfferingStage,
    OfferingStageColor,
    OfferingStageLabel,
    OfferingType,
    OfferingTypeColor,
    OfferingTypeLabel,
} from "@myTypes/offering";
import dayjs from "dayjs";
import CmsLayout from "@layouts/cms/CmsLayout";

const PAGE_SIZE = 10;

const INITIAL_CHECKED_VALUES = [
    "name",
    "startDate",
    "modality",
    "type",
    "stage",
    "format",
    "createdAt",
];

const COLUMN_OPTIONS = [
    { label: "Nombre", value: "name" },
    { label: "Fecha de Inicio", value: "startDate" },
    { label: "Fecha de Fin", value: "endDate" },
    { label: "Duración", value: "duration" },
    { label: "Modalidad", value: "modality" },
    { label: "Tipo", value: "type" },
    { label: "Etapa", value: "stage" },
    { label: "Formato", value: "format" },
    { label: "Fecha de Creación", value: "createdAt" },
    { label: "Fecha de Actualización", value: "updatedAt" },
];

const INITIAL_COLUMNS: TableProps<Offering>["columns"] = [
    {
        title: "NOMBRE",
        dataIndex: "name",
        key: "name",
        render: (name: string, record: Offering) => (
            <Link to={`${record.oid}`} className="text-blue-full font-medium underline">
                {name}
            </Link>
        ),
    },
    {
        title: "FECHA DE INICIO",
        dataIndex: "startDate",
        key: "startDate",
        render: (startDate: string) => {
            const formattedDate = formatDate(startDate);
            return <Text>{formattedDate}</Text>;
        },
    },
    {
        title: "FECHA DE FIN",
        dataIndex: "endDate",
        key: "endDate",
        render: (endDate: string) => {
            const formattedDate = formatDate(endDate);
            return <Text>{formattedDate}</Text>;
        },
    },
    {
        title: "DURACIÓN",
        dataIndex: "duration",
        key: "duration",
        render: (duration: string) => {
            return <Text>{duration}</Text>;
        },
    },
    {
        title: "TIPO",
        dataIndex: "type",
        key: "type",
        render: (type: OfferingType) => (
            <Tag
                bordered={false}
                color={OfferingTypeColor[type]}
                className="rounded-full px-3"
            >
                {OfferingTypeLabel[type]}
            </Tag>
        ),
    },
    {
        title: "FASE",
        dataIndex: "stage",
        key: "stage",
        render: (stage: OfferingStage) => (
            <Tag
                bordered={false}
                color={OfferingStageColor[stage]}
                className="rounded-full px-3"
            >
                {OfferingStageLabel[stage]}
            </Tag>
        ),
    },
    {
        title: "FORMATO",
        dataIndex: "format",
        key: "format",
        render: (format: OfferingFormat) => (
            <Tag
                bordered={false}
                color={format === OfferingFormat.LIVE ? "green" : "volcano"}
                className="rounded-full px-3"
            >
                {OfferingFormatLabel[format]}
            </Tag>
        ),
    },
    {
        title: "ÚLTIMA ACTUALIZACIÓN",
        dataIndex: "updatedAt",
        key: "updatedAt",
        render: (updatedAt: string) => {
            const formattedDate = formatDateTime(updatedAt);
            return <Text>{formattedDate}</Text>;
        },
    },
    {
        title: "FECHA DE CREACIÓN",
        dataIndex: "createdAt",
        key: "createdAt",
        render: (createdAt: string) => {
            const formattedDate = formatDateTime(createdAt);
            return <Text>{formattedDate}</Text>;
        },
    },
];

export default function OfferingListPage() {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const DEFAULT_PAGE = searchParams.get("page")
        ? Number(searchParams.get("page"))
        : 1;
    const [modalOpen, setModalOpen] = useState(false);
    const [currentPage] = useState(DEFAULT_PAGE);
    const [checkedValues, setCheckedValues] =
        useState<string[]>(INITIAL_CHECKED_VALUES);

    const [addForm] = Form.useForm<CreateOfferingFormValues>();

    const { isLoading, data, refetch } = useQuery({
        queryKey: ["offerings", currentPage],
        queryFn: () => listOffering({ page: currentPage }),
    });
    const offerings = data?.results;
    const TOTAL_COUNT = data?.count;

    const handleRowAction = (key: string, record: Offering) => {
        if (key === "edit") {
            navigate(`/cms/offering/${record.oid}`);
        }
    };

    const tableColumns = useMemo(() => {
        return (INITIAL_COLUMNS ?? []).filter((column) =>
            checkedValues.includes(column.key as string),
        );
    }, [checkedValues]);

    const defaultColumn = {
        title: "ACCIONES",
        key: "actions",
        render: (record: Offering) => (
            <Dropdown
                trigger={["click"]}
                menu={{
                    items: [
                        {
                            key: "edit",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-blue-full">
                                        <EditStroke className="w-5 h-5" /> Editar
                                    </div>
                                </>
                            ),
                        },
                        {
                            key: "delete",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-state-red-full">
                                        <DeleteStroke className="w-5 h-5" /> Eliminar
                                    </div>
                                </>
                            ),
                        },
                    ],
                    onClick: ({ key }) => {
                        handleRowAction(key, record);
                    },
                }}
                placement="bottomRight"
            >
                <Button
                    icon={<MoreVertical className="w-5 h-5" />}
                    type="text"
                    size="small"
                />
            </Dropdown>
        ),
    };

    const handleShowHideColumns = (checkedValues: string[]) => {
        setCheckedValues(checkedValues);
    };

    const { mutate } = useMutation({
        mutationFn: (values: CreateOfferingFormValues) => {
            const data: CreateOfferingBody = {
                ...values,
                startDate: values.rangeDate[0].format("YYYY-MM-DD"),
                endDate: values.rangeDate[1].format("YYYY-MM-DD"),
            };
            return createOffering(data);
        },
        onSuccess: () => {
            addForm.resetFields();
            setModalOpen(false);
            refetch();
        },
        onError: () => {
            console.error("Error creating offering");
        },
    });

    const handleFormFinish = (values: CreateOfferingFormValues) => {
        mutate(values);
    };

    return (
        <>
            <CmsLayout>
                <div className="w-full h-full space-y-5">
                    <Modal
                        centered
                        className="max-w-4xl w-full"
                        open={modalOpen}
                        title={
                            <div className="w-full flex justify-center text-2xl py-4">
                                Agregar nuevo Producto
                            </div>
                        }
                        footer={false}
                        onCancel={() => setModalOpen(false)}
                    >
                        <Form
                            name="createOffering"
                            layout="vertical"
                            form={addForm}
                            onFinish={handleFormFinish}
                            initialValues={{
                                rangeDate: [dayjs(), dayjs()],
                                modality: OfferingModality.REMOTE,
                                type: OfferingType.SPECIALIZATION,
                                format: OfferingFormat.LIVE,
                            }}
                        >
                            <Form.Item<CreateOfferingFormValues>
                                name="name"
                                label={
                                    <span className="font-semibold text-base">
                                        Nombre
                                    </span>
                                }
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            "Por favor, ingrese el nombre del programa",
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="Ej. Manejo de Software"
                                    className="py-1"
                                />
                            </Form.Item>
                            <Form.Item<CreateOfferingFormValues>
                                name="slug"
                                label={
                                    <span className="font-semibold text-base">
                                        Slug
                                    </span>
                                }
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            "Por favor, ingrese un slug para el producto",
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="Ej. manejo-de-software"
                                    className="py-1"
                                />
                            </Form.Item>
                            <Form.Item<CreateOfferingFormValues>
                                name="rangeDate"
                                label={
                                    <span className="font-semibold text-base">
                                        Inicio {"y"} Fin
                                    </span>
                                }
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            "Por favor, ingrese las fechas de inicio y fin del programa",
                                    },
                                ]}
                            >
                                <RangePicker className="py-1 w-full" />
                            </Form.Item>
                            <div className="grid grid-cols-2 gap-4">
                                <Form.Item<CreateOfferingFormValues>
                                    name="duration"
                                    label={
                                        <span className="font-semibold text-base">
                                            Duración
                                        </span>
                                    }
                                >
                                    <Input
                                        placeholder="Ej. 3 Semanas"
                                        className="py-1"
                                    />
                                </Form.Item>
                                <Form.Item<CreateOfferingFormValues>
                                    name="frequency"
                                    label={
                                        <span className="font-semibold text-base">
                                            Frecuencia
                                        </span>
                                    }
                                >
                                    <Input
                                        placeholder="Ej. Sábados y Domingos"
                                        className="py-1"
                                    />
                                </Form.Item>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <Form.Item<CreateOfferingFormValues>
                                    name="hours"
                                    label={
                                        <span className="font-semibold text-base">
                                            Horas
                                        </span>
                                    }
                                >
                                    <Input
                                        type="number"
                                        placeholder="Ej. 180"
                                        className="py-1"
                                    />
                                </Form.Item>
                                <Form.Item<CreateOfferingFormValues>
                                    name="schedule"
                                    label={
                                        <span className="font-semibold text-base">
                                            Horario
                                        </span>
                                    }
                                >
                                    <Input
                                        placeholder="Ej. 8:00 am - 10:00 am"
                                        className="py-1"
                                    />
                                </Form.Item>
                            </div>
                            <div className="grid grid-cols-3 gap-4">
                                <Form.Item<CreateOfferingFormValues>
                                    name="basePrice"
                                    label={
                                        <span className="font-semibold text-base">
                                            Precio base (S/.)
                                        </span>
                                    }
                                >
                                    <Input
                                        type="number"
                                        placeholder="Ej. 890.00"
                                        className="py-1"
                                    />
                                </Form.Item>
                                <Form.Item<CreateOfferingFormValues>
                                    name="foreignBasePrice"
                                    label={
                                        <span className="font-semibold text-base">
                                            Precio base ($)
                                        </span>
                                    }
                                >
                                    <Input
                                        type="number"
                                        placeholder="Ej. 890.00"
                                        className="py-1"
                                    />
                                </Form.Item>
                                <Form.Item<CreateOfferingFormValues>
                                    name="discount"
                                    label={
                                        <span className="font-semibold text-base">
                                            Descuento (%)
                                        </span>
                                    }
                                >
                                    <Input placeholder="Ej. 25" className="py-1" />
                                </Form.Item>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <Form.Item<CreateOfferingFormValues>
                                    name="modality"
                                    label={
                                        <span className="font-semibold text-base">
                                            Modalidad
                                        </span>
                                    }
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                "Por favor, seleccione la modalidad del programa",
                                        },
                                    ]}
                                >
                                    <Select
                                        options={[
                                            {
                                                label: OfferingModalityLabel[
                                                    OfferingModality.REMOTE
                                                ],
                                                value: OfferingModality.REMOTE,
                                            },
                                            {
                                                label: OfferingModalityLabel[
                                                    OfferingModality.IN_PERSON
                                                ],
                                                value: OfferingModality.IN_PERSON,
                                            },
                                        ]}
                                    />
                                </Form.Item>
                                <Form.Item<CreateOfferingFormValues>
                                    name="format"
                                    label={
                                        <span className="font-semibold text-base">
                                            Formato
                                        </span>
                                    }
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                "Por favor, seleccione el formato del programa",
                                        },
                                    ]}
                                >
                                    <Select
                                        options={[
                                            {
                                                label: OfferingFormatLabel[
                                                    OfferingFormat.LIVE
                                                ],
                                                value: OfferingFormat.LIVE,
                                            },
                                            {
                                                label: OfferingFormatLabel[
                                                    OfferingFormat.ASYNCHRONOUS
                                                ],
                                                value: OfferingFormat.ASYNCHRONOUS,
                                            },
                                        ]}
                                    />
                                </Form.Item>
                            </div>
                            <Form.Item<CreateOfferingFormValues>
                                name="type"
                                label={
                                    <span className="font-semibold text-base">
                                        Tipo
                                    </span>
                                }
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            "Por favor, seleccione el Tipo del programa",
                                    },
                                ]}
                            >
                                <Select
                                    options={[
                                        {
                                            label: OfferingTypeLabel[
                                                OfferingType.SPECIALIZATION
                                            ],
                                            value: OfferingType.SPECIALIZATION,
                                        },
                                        {
                                            label: OfferingTypeLabel[
                                                OfferingType.PREPARATION
                                            ],
                                            value: OfferingType.PREPARATION,
                                        },
                                        {
                                            label: OfferingTypeLabel[
                                                OfferingType.REVIEW_WORKSHOP
                                            ],
                                            value: OfferingType.REVIEW_WORKSHOP,
                                        },
                                        {
                                            label: OfferingTypeLabel[
                                                OfferingType.UNDERGRADUATE_FORMATION
                                            ],
                                            value: OfferingType.UNDERGRADUATE_FORMATION,
                                        },
                                    ]}
                                />
                            </Form.Item>

                            <div className="grid grid-cols-2 gap-2">
                                <Button
                                    onClick={() => setModalOpen(false)}
                                    className="h-fit"
                                    size="large"
                                >
                                    Cancelar
                                </Button>
                                <Form.Item>
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        className="h-fit"
                                        size="large"
                                        block
                                    >
                                        Guardar
                                    </Button>
                                </Form.Item>
                            </div>
                        </Form>
                    </Modal>
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Gestiona aquí los Productos" />
                        <div className="flex gap-3">
                            <Button
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Import />}
                            >
                                Importar
                            </Button>
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Plus />}
                                onClick={() => setModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </div>
                    <div className="p-5 bg-white-full rounded-lg space-y-5">
                        <div className="flex justify-between items-center">
                            <Text className="text-black-medium text-2xl font-semibold">
                                Productos
                            </Text>

                            <div className="flex items-center gap-3">
                                <Search
                                    size="large"
                                    enterButton
                                    allowClear
                                    onSearch={(value, _, info) => {
                                        console.info(info?.source, value); // TODO: Implement search
                                    }}
                                />
                                <Button
                                    icon={<Reload />}
                                    size="large"
                                    type="text"
                                    onClick={() => refetch()}
                                />
                                <Popover
                                    content={
                                        <div className="p-2 space-y-3">
                                            <div className="uppercase text-black-medium font-medium">
                                                Mostrar/Ocultar Columnas
                                            </div>
                                            <div className="px-2">
                                                <Checkbox.Group
                                                    defaultValue={
                                                        INITIAL_CHECKED_VALUES
                                                    }
                                                    onChange={handleShowHideColumns}
                                                    name="columns"
                                                    className="flex flex-col gap-1"
                                                    options={COLUMN_OPTIONS}
                                                />
                                            </div>
                                        </div>
                                    }
                                    trigger={["click"]}
                                    placement="bottomRight"
                                >
                                    <Button
                                        icon={<Settings />}
                                        size="large"
                                        type="text"
                                    />
                                </Popover>
                            </div>
                        </div>
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        headerBg: "#FBFCFD",
                                        borderColor: "#fff",
                                        headerSplitColor: "#fafafa",
                                        headerBorderRadius: 8,
                                        rowHoverBg: "#F6FAFD",
                                        rowSelectedBg: "#F6FAFD",
                                        rowSelectedHoverBg: "#F6FAFD",
                                        footerBg: "#F1F1F1",
                                    },
                                    Pagination: {
                                        itemSize: 36,
                                        itemActiveBg: "#F6FAFD",
                                    },
                                },
                            }}
                        >
                            <Table
                                rowSelection={{
                                    type: "checkbox",
                                }}
                                locale={{
                                    emptyText: (
                                        <>{isLoading ? <Spinner /> : <Empty />}</>
                                    ),
                                }}
                                columns={
                                    tableColumns ? [...tableColumns, defaultColumn] : []
                                }
                                dataSource={offerings}
                                className="rounded-lg"
                                footer={() => ""}
                                pagination={false}
                            />
                            <div className="flex justify-between">
                                <div className="flex items-center gap-3">
                                    <Button
                                        danger
                                        type="primary"
                                        size="large"
                                        icon={<Trash />}
                                    >
                                        Eliminar
                                    </Button>
                                </div>
                                <div>
                                    <Pagination
                                        defaultCurrent={DEFAULT_PAGE}
                                        total={TOTAL_COUNT}
                                        pageSize={PAGE_SIZE}
                                    />
                                </div>
                            </div>
                        </ConfigProvider>
                    </div>
                </div>
            </CmsLayout>
        </>
    );
}
