import type { Order, OrderItem } from "../../types/order";
import { DollarSign, CoinsIcon } from "lucide-react";
import { Tooltip } from "antd";

type OrderItemPriceCellProps = {
    record: OrderItem;
    order: Order;
    editCustomAmount?: boolean;
};

export default function OrderItemPriceCell({
    record,
    order,
    editCustomAmount // enables editing the custom amount with a EditOrderItemCustomAmountPopover
}: OrderItemPriceCellProps) {
    const isInternational = order.isInternational || false;

    return (
        <div className="flex flex-col space-y-1">
            {!isInternational ? (
                <Tooltip title="Precio en soles">
                    <div className="flex items-center gap-2">
                        <CoinsIcon
                            size={16}
                            className="text-complementary-yellow"
                            strokeWidth={1.75}
                        />
                        <span className="text-sm font-medium text-black-full">
                            S/{" "}
                            {(
                                Number(record.customAmount) ??
                                record.effectiveUnitPrice ??
                                0
                            ).toFixed(2)}
                        </span>
                    </div>
                </Tooltip>
            ) : (
                <Tooltip title="Precio en dólares">
                    <div className="flex items-center gap-2">
                        <DollarSign
                            size={16}
                            className="text-blue-medium"
                            strokeWidth={1.75}
                        />
                        <span className="text-xs text-black-medium">
                            {(
                                Number(record.customAmount) ??
                                record.effectiveUnitPrice ??
                                0
                            ).toFixed(2)}
                        </span>
                    </div>
                </Tooltip>
            )}
        </div>
    );
}