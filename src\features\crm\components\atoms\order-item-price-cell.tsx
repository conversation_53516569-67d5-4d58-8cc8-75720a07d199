import { OrderItem } from "../../types/order";
import { DollarSign, CoinsIcon } from "lucide-react";
import { Tooltip } from "antd";

type OrderItemPriceCellProps = {
    record: OrderItem;
};

export default function OrderItemPriceCell({ record }: OrderItemPriceCellProps) {
    return (
        <div className="flex flex-col space-y-1">
            <Tooltip title="Precio en soles">
                <div className="flex items-center gap-2">
                    <CoinsIcon
                        size={16}
                        className="text-complementary-yellow"
                        strokeWidth={1.75}
                    />
                    <span className="text-sm font-medium text-black-full">
                        S/ {record.unitPrice.toFixed(2)}
                    </span>
                </div>
            </Tooltip>
            <Tooltip title="Precio en dólares">
                <div className="flex items-center gap-2">
                    <DollarSign
                        size={16}
                        className="text-blue-medium"
                        strokeWidth={1.75}
                    />
                    <span className="text-xs text-black-medium">
                        $ {record.foreignUnitPrice.toFixed(2)}
                    </span>
                </div>
            </Tooltip>
        </div>
    );
}
