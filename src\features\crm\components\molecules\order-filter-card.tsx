import { useState } from "react";
import { Card, Form, DatePicker, Select, Button, Row, Col } from "antd";
import { Filter, X, Search } from "lucide-react";
import { OrderStage, OrderStageLabels } from "@/features/crm/types/order";
import { mockOfferings, mockSalesAgents } from "@/features/crm/mock/order-data";
import dayjs from "dayjs";

const { RangePicker } = DatePicker;

// Definición de la interfaz para los filtros
export interface OrderFilters {
    startDate?: string;
    endDate?: string;
    stages?: OrderStage[];
    products?: string[];
    minAmount?: number;
    maxAmount?: number;
    salesAgent?: string;
}

interface OrderFilterProps {
    onFilterChange: (filters: OrderFilters) => void;
}

export default function OrderFilterCard({ onFilterChange }: OrderFilterProps) {
    const [form] = Form.useForm();
    const [expanded, setExpanded] = useState(false);

    const handleReset = () => {
        form.resetFields();
        onFilterChange({});
    };

    // Definición de la interfaz para los valores del formulario
    interface FormValues {
        dateRange?: [dayjs.Dayjs, dayjs.Dayjs];
        stage?: OrderStage[];
        products?: string[];
        amount?: [number, number];
        salesAgent?: string;
    }

    const handleFinish = (values: FormValues) => {
        const filters: OrderFilters = {};

        if (values.dateRange && values.dateRange.length === 2) {
            filters.startDate = values.dateRange[0].format("YYYY-MM-DD");
            filters.endDate = values.dateRange[1].format("YYYY-MM-DD");
        }

        if (values.stage && values.stage.length > 0) {
            filters.stages = values.stage;
        }

        if (values.products && values.products.length > 0) {
            filters.products = values.products;
        }

        if (values.amount && values.amount.length === 2) {
            filters.minAmount = values.amount[0];
            filters.maxAmount = values.amount[1];
        }

        if (values.salesAgent) {
            filters.salesAgent = values.salesAgent;
        }

        onFilterChange(filters);
    };

    const toggleExpanded = () => {
        setExpanded(!expanded);
    };

    return (
        <Card
            className="mb-6 shadow-sm"
            title={
                <div className="flex items-center">
                    <Filter className="mr-2 h-5 w-5 text-blue-500" />
                    <span>Filtrar órdenes</span>
                </div>
            }
            extra={
                <Button
                    type="text"
                    onClick={toggleExpanded}
                    icon={expanded ? <X size={16} /> : <Search size={16} />}
                >
                    {expanded ? "Menos filtros" : "Más filtros"}
                </Button>
            }
        >
            <Form
                form={form}
                layout="vertical"
                onFinish={handleFinish}
                initialValues={{}}
            >
                <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12} lg={8}>
                        <Form.Item name="dateRange" label="Rango de fechas">
                            <RangePicker
                                style={{ width: "100%" }}
                                format="DD/MM/YYYY"
                                placeholder={["Fecha inicio", "Fecha fin"]}
                                ranges={{
                                    Hoy: [dayjs(), dayjs()],
                                    "Esta semana": [
                                        dayjs().startOf("week"),
                                        dayjs().endOf("week"),
                                    ],
                                    "Este mes": [
                                        dayjs().startOf("month"),
                                        dayjs().endOf("month"),
                                    ],
                                    "Último mes": [
                                        dayjs().subtract(1, "month").startOf("month"),
                                        dayjs().subtract(1, "month").endOf("month"),
                                    ],
                                    "Este año": [
                                        dayjs().startOf("year"),
                                        dayjs().endOf("year"),
                                    ],
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col xs={24} sm={12} lg={8}>
                        <Form.Item name="stage" label="Etapa">
                            <Select
                                mode="multiple"
                                allowClear
                                style={{ width: "100%" }}
                                placeholder="Selecciona una etapa"
                                options={Object.values(OrderStage).map((stage) => ({
                                    value: stage,
                                    label: OrderStageLabels[stage],
                                }))}
                            />
                        </Form.Item>
                    </Col>
                    <Col xs={24} sm={12} lg={8}>
                        <Form.Item name="products" label="Productos">
                            <Select
                                mode="multiple"
                                allowClear
                                style={{ width: "100%" }}
                                placeholder="Selecciona un producto"
                                options={mockOfferings.map((offering) => ({
                                    value: offering.oid,
                                    label: offering.name,
                                }))}
                            />
                        </Form.Item>
                    </Col>

                    {expanded && (
                        <>
                            <Col xs={24} sm={12} lg={8}>
                                <Form.Item name="salesAgent" label="Vendedor">
                                    <Select
                                        allowClear
                                        style={{ width: "100%" }}
                                        placeholder="Selecciona un vendedor"
                                        options={[
                                            {
                                                value: "all",
                                                label: "Todos los vendedores",
                                            },
                                            {
                                                value: "unassigned",
                                                label: "Sin asignar",
                                            },
                                            ...mockSalesAgents.map((agent) => ({
                                                value: agent.uid,
                                                label:
                                                    agent.fullName ||
                                                    `${agent.firstName} ${agent.lastName}`,
                                            })),
                                        ]}
                                    />
                                </Form.Item>
                            </Col>
                            <Col xs={24} sm={12} lg={8}>
                                <Form.Item name="amount" label="Rango de monto">
                                    <Select
                                        allowClear
                                        style={{ width: "100%" }}
                                        placeholder="Selecciona un rango"
                                        options={[
                                            {
                                                value: [0, 500],
                                                label: "Menos de S/. 500",
                                            },
                                            {
                                                value: [500, 1000],
                                                label: "S/. 500 - S/. 1,000",
                                            },
                                            {
                                                value: [1000, 3000],
                                                label: "S/. 1,000 - S/. 3,000",
                                            },
                                            {
                                                value: [3000, 5000],
                                                label: "S/. 3,000 - S/. 5,000",
                                            },
                                            {
                                                value: [5000, Infinity],
                                                label: "Más de S/. 5,000",
                                            },
                                        ]}
                                    />
                                </Form.Item>
                            </Col>
                        </>
                    )}
                </Row>

                <div className="flex justify-end gap-2 mt-4">
                    <Button onClick={handleReset}>Limpiar filtros</Button>
                    <Button type="primary" htmlType="submit">
                        Aplicar filtros
                    </Button>
                </div>
            </Form>
        </Card>
    );
}
