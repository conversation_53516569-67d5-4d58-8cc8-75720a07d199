import { useOfferings } from "@/features/crm/hooks/use-offering";
import { Select, type SelectProps } from "antd";
import type { DefaultOptionType } from "antd/es/select";
import { ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";

interface SelectContactProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
}

export default function SelectOfferings({
    value,
    onChange,
    ...restProps
}: SelectContactProps) {
    const { offerings, isLoading } = useOfferings();

    const offeringsOptions: SelectProps["options"] = offerings?.map((offering) => ({
        value: offering.oid,
        label: offering.name,
        data: {
            ...offering,
        },
    }));

    const filterOption = (input: string, option: DefaultOptionType | undefined) => {
        if (!option) return false;
        const searchText = input.toLowerCase();
        const label = String(option.label ?? "").toLowerCase() || "";
        const codeName = option.data?.codeName?.toLowerCase() || "";

        return label.includes(searchText) || codeName.includes(searchText);
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                options={offeringsOptions}
                filterOption={filterOption}
                optionRender={(option) => (
                    <div className="flex justify-between items-center">
                        <div className="text-wrap flex flex-col">
                            <span>{option.data.label}</span>{" "}
                            <span className="text-xs font-semibold text-gray-500">
                                {option.data?.data.codeName}
                            </span>
                        </div>
                        <Link
                            to={`/cms/offering/${option.data.value}`}
                            title="View Offering"
                        >
                            <ExternalLink size={14} />
                        </Link>
                    </div>
                )}
                loading={isLoading}
                allowClear
            />
        </>
    );
}
