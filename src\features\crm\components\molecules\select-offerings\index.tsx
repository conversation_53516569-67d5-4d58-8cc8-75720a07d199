import { useOfferings } from "@/features/crm/hooks/use-offering";
import { Select, SelectProps } from "antd";
import { ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";

interface SelectContactProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
}

export default function SelectOfferings({
    value,
    onChange,
    ...restProps
}: SelectContactProps) {
    const { offerings } = useOfferings();

    const offeringsOptions: SelectProps["options"] = offerings?.map((offering) => ({
        value: offering.oid,
        label: offering.name,
        data: {
            ...offering,
        },
    }));

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                options={offeringsOptions}
                optionRender={(option) => (
                    <div className="flex justify-between items-center">
                        <span>{option.data.label}</span>
                        <Link
                            to={`/cms/offering/${option.data.value}`}
                            title="View Offering"
                        >
                            <ExternalLink size={14} />
                        </Link>
                    </div>
                )}
            />
        </>
    );
}
