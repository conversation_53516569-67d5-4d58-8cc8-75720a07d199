import React from "react";
import { Card } from "antd";
import {
    <PERSON>C<PERSON>,
    <PERSON>,
    <PERSON>s,
    <PERSON><PERSON>s,
    CartesianGrid,
    <PERSON>lt<PERSON>,
    Responsive<PERSON><PERSON>r,
    Legend,
} from "recharts";

type LegendItem = {
    name: string;
    color: string;
};

interface BarChartCardProps {
    title: string;
    data: Array<Record<string, unknown>>;
    dataKey: string;
    xAxisDataKey?: string;
    secondaryDataKey?: string;
    barColor?: string;
    barColors?: string[];
    icon?: React.ReactNode;
    formatter?: (value: number) => string;
    legend?: LegendItem[];
}

const BarChartCard: React.FC<BarChartCardProps> = ({
    title,
    data,
    dataKey,
    xAxisDataKey = "name",
    secondaryDataKey,
    barColor = "#1890ff",
    barColors = ["#1890ff", "#faad14"],
    icon,
    formatter,
    legend,
}) => {
    interface CustomTooltipProps {
        active?: boolean;
        payload?: Array<{
            value: number;
            dataKey: string;
            name: string;
            color: string;
        }>;
        label?: string;
    }

    const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-white-full p-2 border border-gray-200 shadow-md rounded">
                    <p className="font-medium">{`${label}`}</p>
                    {payload.map((entry, index) => (
                        <p key={index} style={{ color: entry.color }}>
                            {legend && legend[index] ? `${legend[index].name}: ` : ""}
                            {formatter ? formatter(entry.value) : entry.value}
                        </p>
                    ))}
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                        data={data}
                        margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                        }}
                    >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                            dataKey={xAxisDataKey}
                            angle={-45}
                            textAnchor="end"
                            height={80}
                            interval={0}
                            tick={{ fontSize: 12 }}
                        />
                        <YAxis
                            tickFormatter={(value) =>
                                formatter
                                    ? formatter(value).split(" ")[0] // For currency formatters
                                    : value
                            }
                        />
                        <Tooltip content={<CustomTooltip />} />
                        {legend && <Legend />}
                        <Bar
                            dataKey={dataKey}
                            fill={barColor}
                            name={legend ? legend[0].name : dataKey}
                            radius={[4, 4, 0, 0]}
                            animationDuration={1500}
                        />
                        {secondaryDataKey && (
                            <Bar
                                dataKey={secondaryDataKey}
                                fill={barColors[1]}
                                name={legend ? legend[1].name : secondaryDataKey}
                                radius={[4, 4, 0, 0]}
                                animationDuration={1500}
                            />
                        )}
                    </BarChart>
                </ResponsiveContainer>
            </div>
        </Card>
    );
};

export default BarChartCard;
