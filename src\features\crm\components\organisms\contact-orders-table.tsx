import { Config<PERSON>rovider, Table, Tag, Tooltip, Typography } from "antd";
import type { TableProps } from "antd";
import { useMemo } from "react";
import { formatDateTime } from "@lib/helpers";
import { Link } from "react-router-dom";
import { DollarSign, Hash } from "lucide-react";
import {
    Order,
    OrderStage,
    OrderStageLabels,
    OrderCurrency,
} from "@/features/crm/types/order";
import { getCurrencyByPhoneNumber } from "@/features/crm/utils/currency";

const { Text } = Typography;

const getStageColor = (stage: OrderStage) => {
    switch (stage) {
        case OrderStage.PROSPECT:
            return "blue";
        case OrderStage.INTERESTED:
            return "green";
        case OrderStage.TO_PAY:
            return "orange";
        case OrderStage.PAID:
            return "purple";
        case OrderStage.LOST:
            return "red";
        default:
            return "default";
    }
};

const INITIAL_COLUMNS: TableProps<Order>["columns"] = [
    {
        title: "ID",
        dataIndex: "oid",
        key: "oid",
        width: 100,
        render: (oid: string) => (
            <Tooltip title="Ver detalles de la orden">
                <Link
                    to={`/crm/orders/${oid}`}
                    className="font-semibold text-blue-600 hover:text-blue-800 flex items-center gap-1"
                >
                    <Hash size={14} strokeWidth={2.5} className="text-gray-500" />
                    {oid.slice(-6)}
                </Link>
            </Tooltip>
        ),
    },
    {
        title: "PROGRAMA",
        dataIndex: "orderItems",
        key: "program",
        render: (items: Order["orderItems"]) => (
            <div className="space-y-1">
                {items?.map((item, index) => (
                    <div key={index} className="flex items-center gap-2">
                        <span className="text-sm font-medium">
                            {item.offering.name}
                        </span>
                    </div>
                ))}
            </div>
        ),
    },
    {
        title: "ESTADO",
        dataIndex: "stage",
        key: "stage",
        width: 150,
        render: (stage: OrderStage) => (
            <Tag color={getStageColor(stage)} className="font-medium">
                {OrderStageLabels[stage]}
            </Tag>
        ),
    },
    {
        title: "PRECIO",
        key: "price",
        width: 180,
        render: (_, record: Order) => {
            const currency = getCurrencyByPhoneNumber(record.owner.phoneNumber);
            const symbol = currency === OrderCurrency.USD ? "$" : "S/";
            return (
                <div className="flex items-center gap-2">
                    <DollarSign
                        size={16}
                        className="text-emerald-600"
                        strokeWidth={1.75}
                    />
                    <span className="font-semibold text-lg">
                        {symbol}
                        {record.agreedTotal?.toLocaleString() || "0.00"}
                    </span>
                    <span
                        className={`text-xs px-2 py-1 rounded-full font-medium ${
                            currency === OrderCurrency.USD
                                ? "bg-emerald-50 text-emerald-800"
                                : "bg-blue-50 text-blue-800"
                        }`}
                    >
                        {currency === OrderCurrency.USD ? "USD" : "PEN"}
                    </span>
                </div>
            );
        },
    },
    {
        title: "FECHA DE PROSPECCIÓN",
        dataIndex: "prospectAt",
        key: "prospectAt",
        width: 200,
        render: (prospectAt: string) => {
            if (!prospectAt) return <span className="text-gray-400">-</span>;
            const formattedDate = formatDateTime(prospectAt);
            return <Text className="text-sm">{formattedDate}</Text>;
        },
    },
    {
        title: "ÚLTIMA ACTUALIZACIÓN",
        dataIndex: "updatedAt",
        key: "updatedAt",
        width: 200,
        render: (updatedAt: string) => {
            const formattedDate = formatDateTime(updatedAt);
            return <Text className="text-sm">{formattedDate}</Text>;
        },
    },
    {
        title: "FECHA DE CREACIÓN",
        dataIndex: "createdAt",
        key: "createdAt",
        width: 200,
        render: (createdAt: string) => {
            const formattedDate = formatDateTime(createdAt);
            return <Text className="text-sm">{formattedDate}</Text>;
        },
    },
];

type ContactOrdersTableProps = {
    orders: Order[];
    loading?: boolean;
};

export default function ContactOrdersTable({
    orders,
    loading,
}: ContactOrdersTableProps) {
    const columns = useMemo(() => INITIAL_COLUMNS, []);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        headerBg: "#FBFCFD",
                        borderColor: "#fff",
                        headerSplitColor: "#fafafa",
                        headerBorderRadius: 8,
                        rowHoverBg: "#F6FAFD",
                        rowSelectedBg: "#F6FAFD",
                        rowSelectedHoverBg: "#F6FAFD",
                        footerBg: "#F1F1F1",
                    },
                },
            }}
        >
            <Table
                className="rounded-lg shadow-sm"
                columns={columns}
                dataSource={orders}
                rowKey="oid"
                loading={loading}
                pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                        `${range[0]}-${range[1]} de ${total} órdenes`,
                }}
                locale={{
                    emptyText: (
                        <div className="text-center py-8">
                            <Text type="secondary" className="text-lg">
                                Este contacto no tiene órdenes registradas
                            </Text>
                        </div>
                    ),
                }}
            />
        </ConfigProvider>
    );
}
