import React from "react";
import { Card, Statistic } from "antd";

interface StatCardProps {
    title: string;
    value: number | string;
    icon: React.ReactNode;
    color: string;
    prefix?: React.ReactNode;
    suffix?: React.ReactNode;
}

const StatCard: React.FC<StatCardProps> = ({
    title,
    value,
    icon,
    color,
    prefix,
    suffix,
}) => {
    return (
        <Card className="shadow-md hover:shadow-lg transition-shadow duration-300">
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm text-gray-500 mb-1">{title}</p>
                    <Statistic
                        value={value}
                        prefix={prefix}
                        suffix={suffix}
                        valueStyle={{ color, fontWeight: "bold" }}
                    />
                </div>
                <div className={`p-3 rounded-full bg-${color}/10`}>{icon}</div>
            </div>
        </Card>
    );
};

export default StatCard;
