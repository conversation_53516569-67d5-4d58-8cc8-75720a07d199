import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Table } from "antd";
import type { TableProps } from "antd";
import { useMemo } from "react";
import type { Order, OrderItem, OrderItemOffering } from "../../types/order";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import OrderItemPriceCell from "../atoms/order-item-price-cell";
import OrderItemOfferingCell from "../atoms/order-item-offering-cell";
import OrderItemDiscountCell from "../atoms/order-item-discount-cell";

type OrderItemsTableProps = {
    order: Order;
};

export default function OrderItemsTable({ order }: OrderItemsTableProps) {
    const handleRemoveProduct = (oiid: string) => {
        console.log("Removing product with ID:", oiid);
    };
    const columns: TableProps<OrderItem>["columns"] = useMemo(
        () => [
            {
                title: "ID",
                dataIndex: "oiid",
                key: "oiid",
                render: (text: string) => (
                    <span className="font-semibold text-gray-600 text-xs">
                        {text?.slice(-6)}
                    </span>
                ),
            },
            {
                title: "PRODUCTO",
                dataIndex: "offering",
                key: "offering",
                render: (content: OrderItemOffering) => (
                    <OrderItemOfferingCell content={content} />
                ),
            },
            {
                title: "DCTO",
                dataIndex: "discount",
                key: "discount",
                render: (_, record: OrderItem) => (
                    <OrderItemDiscountCell record={record} />
                ),
            },
            {
                title: "PRECIO",
                dataIndex: "unitPrice",
                key: "unitPrice",
                render: (_, record: OrderItem) => (
                    <OrderItemPriceCell
                        isInternational={order.isInternational || false}
                        record={record}
                    />
                ),
            },
            {
                title: <Hammer />,
                dataIndex: "oiid",
                key: "oiid",
                render: (oiid: string) => (
                    <Popconfirm
                        title="Quitar producto de la orden"
                        description="¿Estas seguro de quitar este producto?"
                        okText="Si"
                        cancelText="No"
                        onConfirm={() => handleRemoveProduct(oiid)}
                    >
                        <Button type="link" danger icon={<CircleMinus size={16} />} />
                    </Popconfirm>
                ),
            },
        ],
        [order],
    );
    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            headerBg: "#FBFCFD",
                            headerColor: "#7a7a7a",
                            borderColor: "#fff",
                            headerSplitColor: "#fafafa",
                            headerBorderRadius: 8,
                            rowHoverBg: "#F6FAFD",
                            rowSelectedBg: "#F6FAFD",
                            rowSelectedHoverBg: "#F6FAFD",
                            footerBg: "#F1F1F1",
                        },
                    },
                }}
            >
                <Table
                    className="rounded-lg"
                    pagination={false}
                    columns={columns}
                    dataSource={order.orderItems}
                />
            </ConfigProvider>
        </>
    );
}
