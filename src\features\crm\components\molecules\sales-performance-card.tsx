import React from "react";
import { Card, Row, Col, Statistic, Progress, Divider } from "antd";
import { TrendingUp, TrendingDown, Calendar } from "lucide-react";

interface PerformanceData {
    period: string;
    sales: number;
    orders: number;
    conversion: number;
    previousSales?: number;
    previousOrders?: number;
    previousConversion?: number;
}

interface SalesPerformanceCardProps {
    title: string;
    data: PerformanceData;
    icon?: React.ReactNode;
}

export default function SalesPerformanceCard({
    title,
    data,
    icon = <Calendar className="h-5 w-5 text-blue-500" />,
}: SalesPerformanceCardProps) {
    // Calculate percentage changes
    const salesChange = data.previousSales
        ? ((data.sales - data.previousSales) / data.previousSales) * 100
        : 0;

    const ordersChange = data.previousOrders
        ? ((data.orders - data.previousOrders) / data.previousOrders) * 100
        : 0;

    const conversionChange = data.previousConversion
        ? data.conversion - data.previousConversion
        : 0;

    // Helper function to render trend indicators
    const renderTrend = (change: number, isPercentage = false) => {
        const formattedChange = isPercentage
            ? `${change > 0 ? "+" : ""}${change.toFixed(1)}%`
            : `${change > 0 ? "+" : ""}${change.toFixed(1)}`;

        if (change > 0) {
            return (
                <div className="flex items-center text-green-500">
                    <TrendingUp size={16} className="mr-1" />
                    <span>{formattedChange}</span>
                </div>
            );
        } else if (change < 0) {
            return (
                <div className="flex items-center text-red-500">
                    <TrendingDown size={16} className="mr-1" />
                    <span>{formattedChange}</span>
                </div>
            );
        }

        return <span className="text-gray-500">Sin cambio</span>;
    };

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className="shadow-md"
        >
            <div className="text-lg font-medium mb-4">{data.period}</div>

            <Row gutter={[16, 16]}>
                <Col span={8}>
                    <Statistic
                        title="Ventas"
                        value={data.sales}
                        precision={0}
                        valueStyle={{ color: "#3f8600" }}
                        prefix="S/. "
                        formatter={(value) => `${Number(value).toLocaleString()}`}
                    />
                    <div className="mt-2">{renderTrend(salesChange)}</div>
                </Col>

                <Col span={8}>
                    <Statistic
                        title="Órdenes"
                        value={data.orders}
                        valueStyle={{ color: "#1890ff" }}
                    />
                    <div className="mt-2">{renderTrend(ordersChange)}</div>
                </Col>

                <Col span={8}>
                    <Statistic
                        title="Conversión"
                        value={data.conversion}
                        suffix="%"
                        precision={1}
                        valueStyle={{ color: "#722ed1" }}
                    />
                    <div className="mt-2">{renderTrend(conversionChange, true)}</div>
                </Col>
            </Row>

            <Divider orientation="left">Progreso de ventas</Divider>

            <div className="mb-4">
                <div className="flex justify-between mb-1">
                    <span className="text-sm text-gray-500">Meta mensual</span>
                    <span className="text-sm font-medium">S/. 100,000</span>
                </div>
                <Progress
                    percent={Math.min(100, (data.sales / 100000) * 100)}
                    size="small"
                    strokeColor={{
                        "0%": "#108ee9",
                        "100%": "#87d068",
                    }}
                />
                <div className="text-xs text-right mt-1 text-gray-500">
                    {Math.round((data.sales / 100000) * 100)}% completado
                </div>
            </div>
        </Card>
    );
}
