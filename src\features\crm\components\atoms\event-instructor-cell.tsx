import { Event } from "../../types/event";
import { User } from "lucide-react";

type EventInstructorCellProps = {
    instructor: Event["instructor"];
    offering: Event["offering"];
};

export default function EventInstructorCell({
    instructor,
    offering,
}: EventInstructorCellProps) {
    return (
        <div className="px-1 py-1">
            <div className="flex items-center gap-1.5">
                <User size={16} className="text-indigo-600" strokeWidth={2} />
                <span className="text-sm font-semibold text-gray-700">
                    {instructor?.fullName}
                </span>
            </div>

            <div className="mt-1 flex items-start">
                <div className="ml-0.5 pl-5 border-l-2 border-gray-200">
                    <span className="block text-xs text-gray-500 line-clamp-2">
                        {offering.name}
                    </span>
                </div>
            </div>
        </div>
    );
}
