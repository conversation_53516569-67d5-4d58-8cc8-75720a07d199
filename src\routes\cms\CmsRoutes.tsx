import { Routes, Route } from "react-router-dom";
import CmsDashboardPage from "@pages/cms/DashboardPage";
import NotFoundPage from "@pages/cms/NotFoundPage";
import InstructorListPage from "@pages/cms/InstructorListPage";
import InstructorEditPage from "@pages/cms/InstructorEditPage";
import TestimonialListPage from "@pages/cms/TestimonialListPage";
import TestimoniaEditPage from "@pages/cms/TestimonialEditPage";
import OfferingListPage from "@pages/cms/OfferingListPage";
import OfferingEditPage from "@pages/cms/OfferingEditPage";
import BlogEditPage from "@pages/cms/BlogEditPage";
import BlogListPage from "@pages/cms/BlogListPage";
import BlogCategoryListPage from "@pages/cms/BlogCategoryListPage";
import BlogCategoryEditPage from "@pages/cms/BlogCategoryEditPage";
import BlogTagListPage from "@pages/cms/BlogTagListPage";
import BlogTagEditPage from "@pages/cms/BlogTagEditPage";
import BlogEditBlankPage from "@components/cms/organisms/BlogEdit/BlogEditBlankPage";
import BlogPreviewPage from "@pages/cms/BlogPreviewPage";

export default function CmsRoutes() {
    return (
        <Routes>
            <Route path="" element={<CmsDashboardPage />} />
            <Route path="*" element={<NotFoundPage />} />

            {/** Instructor Routes */}
            <Route path="instructor" element={<InstructorListPage />} />
            <Route path="instructor/:iid/edit" element={<InstructorEditPage />} />

            {/** Testimonial Routes */}
            <Route path="testimonial" element={<TestimonialListPage />} />
            <Route path="testimonial/:tid/edit" element={<TestimoniaEditPage />} />

            {/** Offering Routes */}
            <Route path="offering" element={<OfferingListPage />} />
            <Route path="offering/:oid/edit" element={<OfferingEditPage />} />

            {/* Blog Routes */}
            <Route path="blog" element={<BlogListPage />} />
            <Route path="blog/:bid/edit" element={<BlogEditPage />} />
            <Route path="blog/edit" element={<BlogEditBlankPage />} />
            <Route path="blog/:bid/preview" element={<BlogPreviewPage />} />

            {/* Blog categories Routes */}
            <Route path="blog/categories" element={<BlogCategoryListPage />} />
            <Route path="blog/categories/:id/edit" element={<BlogCategoryEditPage />} />

            {/* Blog tags Routes */}
            <Route path="blog/tags" element={<BlogTagListPage />} />
            <Route path="blog/tags/:id/edit" element={<BlogTagEditPage />} />
        </Routes>
    );
}
