import { useMemo } from "react";
import { Row, Col, Select } from "antd";
import {
    Users,
    UserPlus,
    Activity,
    BarChart2,
    Briefcase,
    GraduationCap,
    Globe,
} from "lucide-react";

// Components
import StatCard from "@/features/crm/components/atoms/stat-card";
import BarChartCard from "@/features/crm/components/molecules/bar-chart-card";
import ContactsTable from "@/features/crm/components/organisms/contacts-table";
import RecentActivity from "@/features/crm/components/molecules/recent-activity";
import FilterCard from "@/features/crm/components/molecules/contact-filter-card";

// Mock data
import {
    mockContacts,
    contactsByStatus,
    studentsByMajorChart,
    employeesByCompanyChart,
    recentActivity,
    contactsByCountryChart,
    studentsByUniversityChart,
    // studentsByTermChart,
    contactsWithAccumulatedByMonth,
} from "@/features/crm/mock/contact-data";
import PieChartCard from "@/features/crm/components/molecules/pie-chart-card";
import Composed<PERSON><PERSON>Card from "@/features/crm/components/molecules/charts/composed-chart-card";
import TreeMapCard from "@/features/crm/components/molecules/charts/treemap-chart-card";
import HorizontalBarChartCard from "@/features/crm/components/molecules/charts/horizontal-bar-chart-card";

export default function ContactsDashboardTab() {
    // Filtramos los contactos en base a los filtros seleccionados
    const filteredContacts = useMemo(() => {
        return mockContacts;
        // En un caso real, aquí filtraríamos los contactos según los filtros
    }, []);

    // Colores para los gráficos
    const pieColors = ["#4096ff", "#36cfc9", "#73d13d", "#ff7a45", "#ff4d4f"];
    const countryColors = [
        "#0088FE",
        "#00C49F",
        "#FFBB28",
        "#FF8042",
        "#8884D8",
        "#82CA9D",
        "#FFC658",
    ];
    
    const termColors = ["#73d13d", "#4096ff", "#ff7a45", "#ff4d4f"];

    return (
        <div className="">
            {/* Filtros */}
            <FilterCard />
            {/* Stats Cards */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Total Contactos"
                        value={mockContacts.length}
                        icon={<Users size={24} className="text-blue-500" />}
                        color="#4096ff"
                    />
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Nuevos (este mes)"
                        value={
                            mockContacts.filter((c) => c.createdAt.includes("2024-05"))
                                .length
                        }
                        icon={<UserPlus size={24} className="text-green-500" />}
                        color="#73d13d"
                        suffix={<span className="text-xs text-green-500">+12%</span>}
                    />
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Contactos Activos"
                        value={`${Math.round((contactsByStatus[0].value / mockContacts.length) * 100)}%`}
                        icon={<Activity size={24} className="text-yellow-500" />}
                        color="#faad14"
                    />
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Tasa de Conversión"
                        value="24.5%"
                        icon={<BarChart2 size={24} className="text-purple-500" />}
                        color="#722ed1"
                        suffix={<span className="text-xs text-green-500">+3.2%</span>}
                    />
                </Col>
            </Row>

            {/* Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24}>
                    <ComposedChartCard
                        title="Contactos por Mes"
                        icon={<Users className="h-4 w-4 text-blue-500" />}
                        data={contactsWithAccumulatedByMonth}
                        barDataKey="contacts"
                        lineDataKey="totalAccumulated"
                    />
                </Col>
            </Row>

            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={12}>
                    <TreeMapCard
                        title="Contactos por país"
                        data={contactsByCountryChart}
                        colors={countryColors}
                        icon={<Globe className="h-4 w-4 shrink-0 text-green-500" />}
                        extra={
                            <Select
                                mode="multiple"
                                placeholder="Seleccionar países..."
                                className=" min-w-[200px]"
                                allowClear
                                maxTagCount={1}
                            >
                                {Object.entries(contactsByCountryChart).map(
                                    ([value, d]) => (
                                        <Select.Option key={value} value={value}>
                                            {String(d.name)}
                                        </Select.Option>
                                    ),
                                )}
                            </Select>
                        }
                    />
                </Col>
                <Col xs={24} lg={12}>
                    <HorizontalBarChartCard
                        title="Estudiantes por Universidad"
                        data={studentsByUniversityChart}
                        colors={["#4096ff"]}
                        icon={<GraduationCap className="h-5 w-5 text-blue-500" />}
                    />
                </Col>
            </Row>

            {/* Detailed Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={12}>
                    <PieChartCard
                        title="Estudiantes por Carrera"
                        data={studentsByMajorChart}
                        colors={pieColors}
                        icon={<GraduationCap className="h-5 w-5 text-green-500" />}
                    />
                </Col>
                <Col xs={24} lg={12}>
                    <PieChartCard
                        title="Empleados por Empresa"
                        data={employeesByCompanyChart}
                        colors={pieColors}
                        icon={<Briefcase className="h-5 w-5 text-blue-500" />}
                    />
                </Col>
            </Row>

            {/* Table and Recent Activity */}
            <Row gutter={[16, 16]}>
                <Col xs={24} lg={16}>
                    <ContactsTable contacts={filteredContacts} />
                </Col>
                <Col xs={24} lg={8}>
                    <RecentActivity activities={recentActivity} />
                </Col>
            </Row>
        </div>
    );
}
