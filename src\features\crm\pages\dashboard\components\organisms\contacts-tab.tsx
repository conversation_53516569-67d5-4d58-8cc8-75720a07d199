import { useMemo } from "react";
import { Row, Col, Select } from "antd";
import {
    Users,
    UserPlus,
    Activity,
    BarChart2,
    Briefcase,
    GraduationCap,
    Globe,
    Building2,
} from "lucide-react";

// Components
import StatCard from "@/features/crm/components/atoms/stat-card";
import ContactsTable from "@/features/crm/components/organisms/contacts-table";
import FilterCard from "@/features/crm/components/molecules/contact-filter-card";
import PieChartCard from "@/features/crm/components/molecules/pie-chart-card";
import ComposedChartCard from "@/features/crm/components/molecules/charts/composed-chart-card";
import TreeMapCard from "@/features/crm/components/molecules/charts/treemap-chart-card";
import HorizontalBarChartCard from "@/features/crm/components/molecules/charts/horizontal-bar-chart-card";
import BarChartCard from "@/features/crm/components/molecules/bar-chart-card";
import { useSearchParams } from "react-router-dom";
import { useDashboardContacts } from "@/features/crm/hooks/use-dashboard-contacts";

export default function ContactsDashboardTab() {
    const [searchParams, setSearchParams] = useSearchParams();

    const { data } = useDashboardContacts({
        country: searchParams.get("country") || undefined,
        ocupation: searchParams.get("ocupation") || undefined,
        search: searchParams.get("search") || undefined,
        createdAtBefore: searchParams.get("createdAtBefore") || undefined,
        createdAtAfter: searchParams.get("createdAtAfter") || undefined,
        forceRefresh: searchParams.get("forceRefresh")
            ? searchParams.get("forceRefresh") === "true"
            : undefined,
        active: searchParams.get("active")
            ? searchParams.get("active") === "true"
            : undefined,
    });

    const countryOptions = data?.filterOptions.countries
        ? data.filterOptions.countries.map((country) => ({
              value: country,
              label: country,
          }))
        : [];

    // Colores para los gráficos
    const defaultColor = ["#4096ff"];

    // degradado de azul fuerte  - claro
    const majorColors = [
        // degradado
        "#0088FE",
        "#00C49F",
        "#FFBB28",
        "#FF8042",
        "#8884D8",
        "#82CA9D",
    ];

    const countryColors = [
        "#0088FE",
        "#00C49F",
        "#FFBB28",
        "#FF8042",
        "#8884D8",
        "#82CA9D",
        "#FFC658",
    ];

    const termColors = ["#73d13d", "#4096ff", "#ff7a45", "#ff4d4f"];

    const handleCountryChange = (selectedCountries: string[]) => {
        const currentParams = Object.fromEntries(searchParams.entries());

        if (selectedCountries && selectedCountries.length > 0) {
            // Agregar o actualizar el parámetro country
            currentParams.country = selectedCountries.join(",");
        } else {
            // Remover el parámetro si no hay países seleccionados
            delete currentParams.country;
        }

        setSearchParams(currentParams);
    };

    // Para obtener los países seleccionados desde la URL
    const selectedCountries = useMemo(() => {
        const countryParam = searchParams.get("country");
        return countryParam ? countryParam.split(",") : [];
    }, [searchParams]);

    return (
        <div className="">
            {/* Filtros */}
            <FilterCard />
            {/* Stats Cards */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Total Contactos"
                        value={data?.stats?.totalContacts || 0}
                        icon={<Users size={24} className="text-blue-500" />}
                        color="#4096ff"
                    />
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Nuevos"
                        value={data?.stats.newThisMonth.value || 0}
                        icon={<UserPlus size={24} className="text-green-500" />}
                        color="#73d13d"
                        suffix={
                            data?.stats.newThisMonth.tendency === "up" ? (
                                <span className="text-xs text-green-500">
                                    {"+ "}
                                    {data?.stats.newThisMonth.percentage}
                                </span>
                            ) : data?.stats.newThisMonth.tendency === "down" ? (
                                <span className="text-xs text-state-red-full">
                                    {"- "}
                                    {data?.stats.newThisMonth.percentage}
                                </span>
                            ) : (
                                <></>
                            )
                        }
                    />
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Contactos Activos"
                        value={`${data?.stats?.activePercentage || 0}%`}
                        icon={<Activity size={24} className="text-yellow-500" />}
                        color="#faad14"
                    />
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Tasa de Conversión"
                        info={
                            <div className="space-y-1">
                                <div>
                                    <strong className="text-blue-full/80">
                                        Ventas cerradas entre oportunidades generadas
                                    </strong>
                                </div>
                                <div className="text-xs italic">
                                    <p>Período seleccionado vs anterior equivalente </p>
                                    <p>Ej: Esta semana vs semana pasada</p>
                                </div>
                            </div>
                        }
                        value={`${data?.stats.conversionRate.value || 0}%`}
                        icon={<BarChart2 size={24} className="text-purple-500" />}
                        color="#722ed1"
                        suffix={
                            data?.stats.conversionRate.tendency === "up" ? (
                                <span className="text-xs text-green-500">
                                    {"+ "}
                                    {data?.stats.conversionRate.percentage}
                                </span>
                            ) : data?.stats.conversionRate.tendency === "down" ? (
                                <span className="text-xs text-state-red-full">
                                    {"- "}
                                    {data?.stats.conversionRate.percentage}
                                </span>
                            ) : (
                                <></>
                            )
                        }
                    />
                </Col>
            </Row>

            {/* Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24}>
                    <ComposedChartCard
                        title="Contactos por Mes"
                        icon={<Users className="h-4 w-4 text-blue-500" />}
                        data={data?.contactsByMonth || []}
                        barDataKey="contacts"
                        lineDataKey="totalAccumulated"
                    />
                </Col>
            </Row>

            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={12}>
                    <TreeMapCard
                        title="Contactos por país"
                        data={
                            data?.contactsByCountry.map((item) => ({
                                name: item.country,
                                value: item.contacts,
                            })) || []
                        }
                        colors={countryColors}
                        icon={<Globe className="h-4 w-4 shrink-0 text-green-500" />}
                        extra={
                            <Select
                                mode="multiple"
                                placeholder="Seleccionar países..."
                                className=" min-w-[200px]"
                                allowClear
                                maxTagCount={1}
                                onChange={handleCountryChange}
                                value={selectedCountries}
                            >
                                {countryOptions.map((option) => (
                                    <Select.Option
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </Select.Option>
                                ))}
                            </Select>
                        }
                    />
                </Col>
                <Col xs={24} lg={12}>
                    <HorizontalBarChartCard
                        title="Estudiantes por Universidad"
                        data={data?.studentsByUniversity || []}
                        colors={defaultColor}
                        icon={<GraduationCap className="h-5 w-5 text-blue-500" />}
                    />
                </Col>
            </Row>

            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={12}>
                    <BarChartCard
                        title="Contactos por Ciclo Académico"
                        data={data?.studentsByTerm || []}
                        dataKey="value"
                        barColors={termColors}
                    />
                </Col>
                <Col xs={24} lg={12}>
                    <HorizontalBarChartCard
                        title="Estudiantes por Carrera"
                        data={data?.studentsByMajor || []}
                        colors={defaultColor}
                        icon={<GraduationCap className="h-5 w-5 text-green-500" />}
                    />
                </Col>
            </Row>

            {/* Detailed Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={12}>
                    <PieChartCard
                        title="Empleados por Empresa"
                        data={data?.employeesByCompany || []}
                        colors={majorColors}
                        icon={<Building2 className="h-5 w-5 text-blue-500" />}
                    />
                </Col>
                <Col xs={24} lg={12}>
                    <PieChartCard
                        title="Contactos por Ocupación"
                        data={data?.contactsByOccupation || []}
                        colors={majorColors}
                        icon={<Briefcase className="h-5 w-5 text-blue-500" />}
                    />
                </Col>
            </Row>

            {/* Table and Recent Activity */}
            <Row gutter={[16, 16]}>
                <Col xs={24}>
                    <ContactsTable contacts={data?.recentActivityContacts || []} />
                </Col>
            </Row>
        </div>
    );
}
