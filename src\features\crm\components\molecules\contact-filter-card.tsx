import { useEffect, useRef } from "react";
import { Card, Select, DatePicker, Input, Button, Form } from "antd";
import { ContactOcupation, ContactOcupationLabel } from "@/features/crm/types/contact";
import { SearchIcon, RefreshCwIcon, Filter } from "lucide-react";
import locale from "antd/es/date-picker/locale/es_ES";
import dayjs, { Dayjs } from "dayjs";
import { useMutateSearchParams } from "@hooks/use-mutate-search-params";
import { parseFilterToRecord } from "@lib/filters";

const { RangePicker } = DatePicker;
const { Option } = Select;

interface FilterValues {
    search?: string;
    createdAt?: [Dayjs, Dayjs] | null;
    ocupation?: ContactOcupation[];
    active?: boolean | null;
}

export default function ContactFilterCard() {
    const [form] = Form.useForm<FilterValues>();
    const { searchParams, mutateManySearchParams } = useMutateSearchParams();

    // Capturar valores iniciales
    const initialValuesRef = useRef<FilterValues | null>(null);

    if (initialValuesRef.current === null) {
        const values: FilterValues = {
            ...Object.fromEntries(searchParams.entries()),
        };

        values.active = searchParams.get("active")
            ? searchParams.get("active") === "true"
            : undefined;

        values.ocupation = searchParams
            .get("ocupation")
            ?.split(",") as ContactOcupation[];

        // Date range
        const createdAtAfter = searchParams.get("createdAtAfter");
        const createdAtBefore = searchParams.get("createdAtBefore");
        if (createdAtAfter && createdAtBefore) {
            values.createdAt = [dayjs(createdAtAfter), dayjs(createdAtBefore)];
        } else {
            // this week
            values.createdAt = [dayjs().startOf("week"), dayjs().endOf("week")];
        }

        initialValuesRef.current = values;
    }

    const getInitialValues = () => {
        if (!initialValuesRef.current) return {};
        return {
            search: "",
            ...initialValuesRef.current,
            createdAt: [
                initialValuesRef.current.createdAt?.[0] as Dayjs,
                initialValuesRef.current.createdAt?.[1] as Dayjs,
            ],
        };
    };

    // Establecer valores iniciales solo una vez
    useEffect(() => {
        if (initialValuesRef.current) {
            form.setFieldsValue(getInitialValues());
        }
    }, [form]);

    const handleApplyFilters = () => {
        const values = form.getFieldsValue();
        const newParams = parseFilterToRecord(values);
        mutateManySearchParams(newParams);
    };

    const handleClearFilters = () => {
        // Limpiar los search params y resetear el formulario al mismo tiempo
        const emptyParams: Record<string, string | null> = {
            search: "",
            createdAtAfter:
                initialValuesRef.current?.createdAt?.[0]?.format("YYYY-MM-DD") || null,
            createdAtBefore:
                initialValuesRef.current?.createdAt?.[1]?.format("YYYY-MM-DD") || null,
            ocupation: null,
            active: null,
        };

        form.setFieldsValue(getInitialValues());
        mutateManySearchParams(emptyParams);
    };

    return (
        <Card
            className="shadow-md mb-6"
            title={
                <div className="flex items-center">
                    <Filter className="mr-2 h-5 w-5 text-blue-500" />
                    <span>Filtrar contactos</span>
                </div>
            }
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={undefined}
                onFinish={handleApplyFilters}
            >
                <div className="flex flex-wrap gap-4">
                    <div className="flex-grow min-w-[200px]">
                        <Form.Item name="search" label="Buscar" className="mb-0">
                            <Input
                                placeholder="Nombre, email o teléfono"
                                prefix={
                                    <SearchIcon className="h-4 w-4 text-gray-400" />
                                }
                                allowClear
                            />
                        </Form.Item>
                    </div>

                    <div className="flex-grow min-w-[200px]">
                        <Form.Item
                            name="createdAt"
                            label="Fecha de registro"
                            className="mb-0"
                        >
                            <RangePicker
                                className="w-full"
                                locale={locale}
                                presets={[
                                    {
                                        label: "Hoy",
                                        value: [dayjs(), dayjs()],
                                    },
                                    {
                                        label: "Esta semana",
                                        value: [
                                            dayjs().startOf("week"),
                                            dayjs().endOf("week"),
                                        ],
                                    },
                                    {
                                        label: "Este mes",
                                        value: [
                                            dayjs().startOf("month"),
                                            dayjs().endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Último mes",
                                        value: [
                                            dayjs()
                                                .subtract(1, "month")
                                                .startOf("month"),
                                            dayjs().subtract(1, "month").endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Este año",
                                        value: [
                                            dayjs().startOf("year"),
                                            dayjs().endOf("year"),
                                        ],
                                    },
                                ]}
                            />
                        </Form.Item>
                    </div>

                    <div className="min-w-[150px]">
                        <Form.Item name="ocupation" label="Ocupación" className="mb-0">
                            <Select
                                mode="multiple"
                                placeholder="Seleccionar"
                                className="w-full"
                                allowClear
                                maxTagCount={1}
                            >
                                {Object.entries(ContactOcupationLabel).map(
                                    ([value, label]) => (
                                        <Option key={value} value={value}>
                                            {label}
                                        </Option>
                                    ),
                                )}
                            </Select>
                        </Form.Item>
                    </div>

                    <div className="min-w-[150px]">
                        <Form.Item name="active" label="Estado" className="mb-0">
                            <Select
                                placeholder="Seleccionar"
                                className="w-full"
                                allowClear
                            >
                                <Option value={true}>Activo</Option>
                                <Option value={false}>Inactivo</Option>
                            </Select>
                        </Form.Item>
                    </div>

                    <div className="flex justify-end items-end gap-2 mt-4">
                        <Button
                            type="default"
                            icon={<RefreshCwIcon className="h-4 w-4" />}
                            onClick={handleClearFilters}
                        >
                            Limpiar filtros
                        </Button>
                        <Button type="primary" htmlType="submit">
                            Aplicar filtros
                        </Button>
                    </div>
                </div>
            </Form>
        </Card>
    );
}
