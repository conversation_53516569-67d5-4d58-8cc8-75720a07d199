import { Routes, Route } from "react-router-dom";
import CrmDashboardPage from "./pages/dashboard";
import NotFoundPage from "./pages/not-found";
import ContactsListPage from "./pages/contacts/list";
import ContactDetailPage from "./pages/contacts/detail";
import OrdersListPage from "./pages/orders/list";
import PaymentsListPage from "./pages/payments/list";
import PaymentsDetailPage from "./pages/payments/detail";
import EventsListPage from "./pages/events/list";
import EventsDetailPage from "./pages/events/detail";
import OrdersDetailPage from "./pages/orders/detail";
import EventSchedulesListPage from "./pages/event-schedules/list";
import EventScheduleDetailPage from "./pages/event-schedules/detail";
export default function CrmRoutes() {
    return (
        <Routes>
            <Route path="" element={<CrmDashboardPage />} />
            {/** Contacts Routes */}
            <Route path="contacts" element={<ContactsListPage />} />
            <Route path="contacts/:cid" element={<ContactDetailPage />} />

            {/** Sales Routes */}
            <Route path="orders" element={<OrdersListPage />} />
            <Route path="orders/:oid" element={<OrdersDetailPage />} />

            {/** Payments Routes */}
            <Route path="payments" element={<PaymentsListPage />} />
            <Route path="payments/:pid" element={<PaymentsDetailPage />} />

            {/** Events Routes */}
            <Route path="events" element={<EventsListPage />} />
            <Route path="events/:eid" element={<EventsDetailPage />} />

            {/** Event Schedules Routes */}
            <Route path="event-schedules" element={<EventSchedulesListPage />} />
            <Route path="event-schedules/:esid" element={<EventScheduleDetailPage />} />

            {/** Not Found Route */}
            <Route path="*" element={<NotFoundPage />} />
        </Routes>
    );
}
