import { UploadFile } from "antd";
import { Dayjs } from "dayjs";

// Enums for various choices
export enum OfferingModality {
    REMOTE = "REMOTE",
    IN_PERSON = "IN_PERSON",
}

export const OfferingModalityLabel: Record<OfferingModality, string> = {
    [OfferingModality.REMOTE]: "Remoto",
    [OfferingModality.IN_PERSON]: "Presencial",
};

export enum OfferingType {
    SPECIALIZATION = "SPECIALIZATION",
    PREPARATION = "PREPARATION",
    REVIEW_WORKSHOP = "REVIEW_WORKSHOP",
    UNDERGRADUATE_FORMATION = "UNDERGRADUATE_FORMATION",
}

export const OfferingTypeLabel: Record<OfferingType, string> = {
    [OfferingType.SPECIALIZATION]: "Especialización",
    [OfferingType.PREPARATION]: "Preparación",
    [OfferingType.REVIEW_WORKSHOP]: "Taller",
    [OfferingType.UNDERGRADUATE_FORMATION]: "Formación Universitaria",
};

export const OfferingTypeColor: Record<OfferingType, string> = {
    [OfferingType.SPECIALIZATION]: "#39A9FF",
    [OfferingType.PREPARATION]: "#B6B817",
    [OfferingType.REVIEW_WORKSHOP]: "#9747FF",
    [OfferingType.UNDERGRADUATE_FORMATION]: "#F7941D",
};

export enum OfferingStage {
    PLANNING = "PLANNING",
    LAUNCHED = "LAUNCHED",
    ENROLLMENT = "ENROLLMENT",
    ENROLLMENT_CLOSED = "ENROLLMENT_CLOSED",
    FINISHED = "FINISHED",
}

export const OfferingStageLabel: Record<OfferingStage, string> = {
    [OfferingStage.PLANNING]: "En Planificación",
    [OfferingStage.LAUNCHED]: "Lanzado",
    [OfferingStage.ENROLLMENT]: "Matrículas Abiertas",
    [OfferingStage.ENROLLMENT_CLOSED]: "Matrículas Cerradas",
    [OfferingStage.FINISHED]: "Finalizado",
};

export const OfferingStageColor: Record<OfferingStage, string> = {
    [OfferingStage.PLANNING]: "orange",
    [OfferingStage.LAUNCHED]: "green",
    [OfferingStage.ENROLLMENT]: "blue",
    [OfferingStage.ENROLLMENT_CLOSED]: "purple",
    [OfferingStage.FINISHED]: "red",
};

export enum OfferingFormat {
    LIVE = "LIVE",
    ASYNCHRONOUS = "ASYNCHRONOUS",
}

export const OfferingFormatLabel: Record<OfferingFormat, string> = {
    [OfferingFormat.LIVE]: "En Vivo",
    [OfferingFormat.ASYNCHRONOUS]: "Asincrónico",
};

export type OfferingThumbnail = {
    fid: string;
    name: string;
    url: string;
};

export type OfferingObjective = {
    title: string;
    description: string;
};

export type Topic = {
    tid?: string;
    title: string;
};

export type Course = {
    mcid?: string;
    title: string;
    topics: Topic[];
};

export type Module = {
    omid?: string;
    title: string;
    courses: Course[];
};

export type OfferingInstructor = {
    iid: string;
};

export type Offering = {
    oid: string;
    key: string;
    name: string;
    slug: string;
    startDate: string;
    endDate: string;
    description?: string;
    duration?: string;
    frequency?: string;
    hours?: number;
    schedule?: string;
    modality: OfferingModality;
    type: OfferingType;
    stage: OfferingStage;
    format: OfferingFormat;
    basePrice: number;
    foreignBasePrice: number;
    discount: number;
    finalPrice: number;
    foreignFinalPrice: number;
    thumbnail?: OfferingThumbnail | null;
    order: number;
    createdAt: string | Date;
    updatedAt: string | Date;

    objectives: OfferingObjective[];
    modules: Module[];
    instructors: OfferingInstructor[] | string[];
};

export type CreateOfferingFormValues = Partial<
    Omit<Offering, "oid" | "key" | "createdAt" | "updatedAt" | "finalPrice">
> & {
    rangeDate: [Dayjs, Dayjs];
    thumbnailFile?: File[];
};

export type CreateOfferingBody = Partial<
    Omit<
        Offering,
        | "oid"
        | "key"
        | "createdAt"
        | "updatedAt"
        | "thumbnail"
        | "stage"
        | "description"
        | "finalPrice"
    >
>;

export type PartialUpdateOfferingBody = Partial<
    Omit<Offering, "oid" | "key" | "createdAt" | "updatedAt" | "finalPrice">
> & {
    thumbnailFile?: UploadFile[];
    deleteThumbnail?: boolean;
    rangeDate?: [Dayjs, Dayjs];
};
