import { useMutation, useQuery } from "@tanstack/react-query";
import {
    createOrder,
    listOrders,
    getOrdersByContact,
    retrieveOrder,
    ListOrdersQueryParams,
    partialUpdateOrderItem,
} from "../services/portals/order";
import { AxiosError } from "axios";
import { CreateOrder, Order, OrderItem } from "../types/order";
import queryClient from "@lib/queryClient";

export const useOrders = (queryParams?: ListOrdersQueryParams) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["orders", queryParams],
        queryFn: () => listOrders(queryParams),
    });

    const { count, results: orders } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        orders,
        count,
    };
};

export const useOrdersByContact = (contactId: string) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["orders", "contact", contactId],
        queryFn: () => getOrdersByContact(contactId),
        enabled: !!contactId,
    });

    const { count, results: orders } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        orders,
        count,
    };
};

export const useOrder = (oid: string) => {
    const {
        data: order,
        isLoading,
        isError,
    } = useQuery({
        queryKey: ["order", oid],
        queryFn: () => retrieveOrder(oid),
        enabled: !!oid,
        refetchOnWindowFocus: false,
        retry: false,
    });

    return {
        isLoading,
        isError,
        order,
    };
};

type UseCreateOrderProps = {
    onCreateOrderSuccess?: (order: Order) => void;
    onCreateOrderError?: () => void;
};

export const useCreateOrder = ({
    onCreateOrderSuccess,
    onCreateOrderError,
}: UseCreateOrderProps = {}) => {
    return useMutation<Order, AxiosError, CreateOrder>({
        mutationFn: (newOrder: CreateOrder) => createOrder(newOrder),

        onSuccess: (data) => {
            onCreateOrderSuccess?.(data);
            queryClient.invalidateQueries({ queryKey: ["orders"] });
        },
        onError: (error) => {
            console.error("Error creating order:", error);
            onCreateOrderError?.();
        },
    });
};

type UseUpdateOrdenItemProps = {
    onUpdateOrderItemSuccess?: (item: Partial<OrderItem>) => void;
    onUpdateOrderItemError?: () => void;
};

export const useUpdateOrderItem = ({
    onUpdateOrderItemSuccess,
    onUpdateOrderItemError,
}: UseUpdateOrdenItemProps = {}) => {
    return useMutation<
        Partial<OrderItem>,
        AxiosError,
        { oid: string; orderItem: Partial<OrderItem> }
    >({
        mutationFn: ({ oid, orderItem }) => partialUpdateOrderItem(oid, orderItem),

        onSuccess: (item, variables) => {
            onUpdateOrderItemSuccess?.(item);
            queryClient.invalidateQueries({ queryKey: ["order", variables.oid] });
        },
        onError: () => {
            onUpdateOrderItemError?.();
        },
    });
};
