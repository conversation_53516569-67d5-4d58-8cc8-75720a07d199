type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

export enum OrderStage {
    PROSPECT = "prospect",
    INTERESTED = "interested",
    TO_PAY = "to_pay",
    SOLD = "sold",
    LOST = "lost",
}

export const OrderStageLabels: Record<OrderStage, string> = {
    [OrderStage.PROSPECT]: "Prospecto",
    [OrderStage.INTERESTED]: "Interesado",
    [OrderStage.TO_PAY]: "Por pagar",
    [OrderStage.SOLD]: "Vendido",
    [OrderStage.LOST]: "Perdido",
};

export enum OrderCurrency {
    USD = "usd",
    PEN = "pen",
}

export const OrderCurrencyLabels: Record<OrderCurrency, string> = {
    [OrderCurrency.USD]: "Dólares",
    [OrderCurrency.PEN]: "Soles",
};

type OrderContact = {
    uid: string;
    firstName?: string;
    lastName?: string;
    fullName?: string;
    email?: string;
    phoneNumber: string;
};

export type OrderItemOffering = {
    oid: string;
    name: string;
    slug: string;
};

export type OrderItem = {
    oiid: string;
    key: string;
    offering: OrderItemOffering;
    quantity: number;
    basePrice: number;
    foreignBasePrice: number;
    discount: number;
    unitPrice: number;
    totalPrice: number;
    foreignUnitPrice: number;
} & Partial<AuditBaseType>;

export type Order = {
    oid: string;
    owner: OrderContact;
    stage: OrderStage;
    orderItems: OrderItem[];

    prospectAt?: string;
    interestedAt?: string;
    toPayAt?: string;
    soldAt?: string;
    lostAt?: string;

    agreedTotal?: number;
    salesAgent?: OrderContact;
} & AuditBaseType;

export type StageDate = {
    stage: OrderStage;
    date: string;
};

export type OrderBenefit = {
    bid: string;
    name: string;
    description: string;
} & AuditBaseType;

export type OrderLeadSource = {
    lsid: string;
    name: string;
} & AuditBaseType;

export type RetrieveOrder = {
    oid: string;
    owner: OrderContact;
    stage: OrderStage;
    salesAgent: OrderContact;
    orderItems: OrderItem[];
    stagesDates: StageDate[];
    benefits: OrderBenefit[];
    leadSources: OrderLeadSource[];
    agreedTotal?: number;
} & AuditBaseType;

export type CreateOrderFormValues = {
    owner: string;
    stage: OrderStage;
    products: string[];
    benefits: string[];
};

export type CreateOrder = {
    owner: string;
    stage: OrderStage;
    products: string[];
    benefits: string[];
};

export type PartialUpdateOrderValues = {
    owner: string;
    salesAgent: string;
    benefits: string[];
    agreedTotal: number;
    leadSources: string[];
};

export type OrderPartialUpdate = {
    owner: string;
    salesAgent?: string;
    benefits?: string[];
    leadSource?: string[];
    stage?: OrderStage;
    prospectedAt?: string;
    interestedAt?: string;
    toPayAt?: string;
    paidAt?: string;
    lostAt?: string;
};