import { ConfigProvider, Table, Tag, Tooltip } from "antd";
import { Typography } from "antd";
import type { ColumnsType } from "antd/es/table";
import { DollarSign, Hash } from "lucide-react";
import {
    Order,
    OrderCurrency,
    OrderStage,
    OrderStageLabels,
} from "@/features/crm/types/order";
import { getCurrencyByPhoneNumber } from "@/features/crm/utils/currency";
import DateCell from "../atoms/date-cell";
import { Link } from "react-router-dom";
import ContactMethods from "../atoms/contact-methods";

const { Text } = Typography;

interface OrdersTableProps {
    orders: Order[];
}

const getStageColor = (stage: OrderStage) => {
    switch (stage) {
        case OrderStage.PROSPECT:
            return "blue";
        case OrderStage.INTERESTED:
            return "green";
        case OrderStage.TO_PAY:
            return "orange";
        case OrderStage.SOLD:
            return "purple";
        case OrderStage.LOST:
            return "red";
        default:
            return "default";
    }
};

export default function OrdersTable({ orders }: OrdersTableProps) {
    const columns: ColumnsType<Order> = [
        {
            title: "ORDEN",
            dataIndex: "oid",
            key: "oid",
            render: (oid: Order["oid"]) => (
                <Tooltip title="Ver detalles de la orden">
                    <Link
                        to={`/crm/orders/${oid}`}
                        className="font-semibold text-blue-600 hover:text-blue-800 flex items-center gap-1"
                    >
                        <Hash size={14} strokeWidth={2.5} className="text-gray-500" />
                        {oid.slice(-6)}
                    </Link>
                </Tooltip>
            ),
        },
        {
            title: "CLIENTE",
            dataIndex: "owner",
            key: "owner",
            render: (owner: Order["owner"]) => (
                <>
                    <div className="flex items-center gap-1 mb-1">
                        <Link
                            to={`/crm/contacts/${owner.uid}`}
                            className="font-medium text-blue-full hover:underline"
                        >
                            {owner.fullName ||
                                `${owner.firstName || ""} ${owner.lastName || ""}` ||
                                "Sin nombre"}
                        </Link>
                    </div>
                    <ContactMethods
                        phoneNumbers={owner.phoneNumber}
                        emails={owner.email}
                        showLabels={false}
                        maxVisible={1}
                    />
                </>
            ),
        },
        {
            title: "PROGRAMAS",
            dataIndex: "orderItems",
            key: "program",
            render: (items: Order["orderItems"]) => (
                <div className="space-y-1">
                    {items?.map((item, index) => (
                        <div key={index} className="flex items-center gap-2">
                            <span className="text-sm font-medium text-gray-900">
                                {item.offering.name}
                            </span>
                        </div>
                    ))}
                </div>
            ),
        },
        {
            title: "ESTADO",
            dataIndex: "stage",
            key: "stage",
            render: (stage: OrderStage) => (
                <Tag color={getStageColor(stage)}>{OrderStageLabels[stage]}</Tag>
            ),
        },
        {
            title: "Precio",
            key: "price",
            render: (_, record) => {
                const currency = getCurrencyByPhoneNumber(record.owner.phoneNumber);
                const symbol = currency === OrderCurrency.USD ? "$" : "S/";
                return (
                    <div className="flex items-center">
                        <DollarSign size={14} className="text-green-600 mr-1" />
                        <span className="font-medium">
                            {symbol}
                            {record.total?.toLocaleString() || "1,500.00"}
                        </span>
                        <Tag
                            className="ml-2"
                            color={currency === OrderCurrency.USD ? "blue" : "green"}
                        >
                            {currency === OrderCurrency.USD ? "USD" : "PEN"}
                        </Tag>
                    </div>
                );
            },
        },
        {
            title: "FECHAS",
            dataIndex: "dates",
            key: "createdAt",
            render: (_: string, record: Order) => (
                <div className="space-y-2">
                    <DateCell date={record.createdAt} label="Creación" />
                    <DateCell date={record.updatedAt} label="Actualización" />
                </div>
            ),
        },
    ];

    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        headerBg: "#FBFCFD",
                        borderColor: "#fff",
                        headerSplitColor: "#fafafa",
                        headerBorderRadius: 8,
                        rowHoverBg: "#F6FAFD",
                        rowSelectedBg: "#F6FAFD",
                        rowSelectedHoverBg: "#F6FAFD",
                        footerBg: "#F1F1F1",
                    },
                },
            }}
        >
            <Table
                className="rounded-lg shadow-sm"
                columns={columns}
                dataSource={orders}
                rowKey="oid"
                locale={{
                    emptyText: (
                        <div className="text-center py-4">
                            <Text type="secondary">No hay órdenes para mostrar</Text>
                        </div>
                    ),
                }}
                pagination={false}
            />
        </ConfigProvider>
    );
}
