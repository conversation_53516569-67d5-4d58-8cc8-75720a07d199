import { <PERSON><PERSON>, <PERSON><PERSON>icker, Divider, Form, InputNumber, Switch } from "antd";
import { PaymentCreateForm, PaymentCreateRequest } from "@/features/crm/types/payment";
import SelectOrder from "@/features/crm/components/molecules/select-order";
import RadioCurrency from "@/features/crm/components/molecules/radio-currency";
import { OrderCurrency, RetrieveOrder } from "@/features/crm/types/order";
import SelectPaymentMethod from "@/features/crm/components/molecules/select-payment-method";
import UploadVoucher from "@/features/crm/components/molecules/upload-voucher";
import { useCreatePayment } from "../../hooks/use-payment";
import dayjs from "dayjs";

type CreatePaymentFormProps = {
    onFinish?: () => void;
    preselectedOrder?: RetrieveOrder;
};

export default function CreatePaymentForm({
    onFinish,
    preselectedOrder,
}: CreatePaymentFormProps) {
    const [createPaymentForm] = Form.useForm();
    const isPaid = Form.useWatch(["isPaid"], createPaymentForm);
    const currency = Form.useWatch(["currency"], createPaymentForm);

    const handleOnCreatePaymentSuccess = () => {
        createPaymentForm.resetFields();
        onFinish?.();
    };

    const handleOnCreatePaymentError = () => {
        createPaymentForm.resetFields();
        onFinish?.();
    };

    const { mutate: createPaymentMutate } = useCreatePayment({
        onCreatePaymentSuccess: handleOnCreatePaymentSuccess,
        onCreatePaymentError: handleOnCreatePaymentError,
    });

    const handleOnFinish = (values: PaymentCreateForm) => {
        const { paymentDate: dayjsPaymentDate, voucher: voucherFile, ...rest } = values;

        const paymentDate = isPaid
            ? new Date().toISOString()
            : dayjs(dayjsPaymentDate).toISOString();
        const voucher = voucherFile?.[0]?.response?.fid;

        const paymentCreateRequest: PaymentCreateRequest = {
            ...rest,
            paymentDate: paymentDate,
            voucher,
        };
        createPaymentMutate(paymentCreateRequest);
    };

    return (
        <Form
            name="create-payment-form"
            layout="vertical"
            form={createPaymentForm}
            onFinish={handleOnFinish}
            initialValues={{
                currency: OrderCurrency.PEN,
                isPaid: true,
                amount: 0,
                order: preselectedOrder?.oid,
            }}
        >
            <Form.Item<PaymentCreateForm>
                name="order"
                label={<span className="font-semibold">Orden</span>}
                rules={[{ required: true, message: "Por favor seleccione una orden" }]}
            >
                <SelectOrder disabled={!!preselectedOrder} />
            </Form.Item>
            <div className="grid grid-cols-3">
                <Form.Item<PaymentCreateForm>
                    name="isPaid"
                    label={<span className="font-semibold">¿Pagar ahora?</span>}
                >
                    <Switch />
                </Form.Item>
                <Form.Item<PaymentCreateForm>
                    name="currency"
                    label={<span className="font-semibold">Moneda</span>}
                >
                    <RadioCurrency />
                </Form.Item>
                <Form.Item<PaymentCreateForm>
                    name="amount"
                    label={<span className="font-semibold">Amount</span>}
                    rules={[{ required: true, message: "Por favor ingrese el monto" }]}
                >
                    <InputNumber
                        prefix={currency === OrderCurrency.PEN ? "S/ " : "$ "}
                        min={0}
                        className="w-full"
                    />
                </Form.Item>
            </div>
            {isPaid ? (
                <Form.Item<PaymentCreateForm>
                    name="voucher"
                    label={<span className="font-semibold">Voucher</span>}
                    valuePropName="listFile"
                >
                    <UploadVoucher />
                </Form.Item>
            ) : (
                <Form.Item<PaymentCreateForm>
                    name="paymentDate"
                    label={<span className="font-semibold">Fecha de pago</span>}
                    rules={
                        !isPaid
                            ? [
                                  {
                                      required: true,
                                      message: "Por favor ingrese la fecha de pago",
                                  },
                              ]
                            : []
                    }
                >
                    <DatePicker className="w-full" />
                </Form.Item>
            )}

            <Form.Item<PaymentCreateForm>
                name="paymentMethod"
                label={<span className="font-semibold">Método de pago</span>}
            >
                <SelectPaymentMethod />
            </Form.Item>

            <Divider />

            <div className="grid grid-cols-2 gap-2 items-end">
                <Button onClick={() => {}} className="h-fit" size="large">
                    Cancelar
                </Button>
                <Button
                    type="primary"
                    htmlType="submit"
                    className="h-fit"
                    size="large"
                    block
                >
                    Guardar
                </Button>
            </div>
        </Form>
    );
}
