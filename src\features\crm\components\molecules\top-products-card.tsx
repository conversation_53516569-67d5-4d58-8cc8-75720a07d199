import React from "react";
import { Card, Table, Tag, Progress } from "antd";
import { DollarSign, Package, ArrowUpRight } from "lucide-react";

interface TopProductsCardProps {
    title: string;
    data: Array<{
        name: string;
        count: number;
        revenue: number;
    }>;
    icon?: React.ReactNode;
    maxRevenue?: number; // Used to calculate the proportion for the progress bar
}

export default function TopProductsCard({
    title,
    data,
    icon = <Package className="h-5 w-5 text-blue-500" />,
    maxRevenue,
}: TopProductsCardProps) {
    // If maxRevenue wasn't provided, calculate it from the data
    const calculatedMaxRevenue =
        maxRevenue || Math.max(...data.map((item) => item.revenue), 0);

    const columns = [
        {
            title: "Producto",
            dataIndex: "name",
            key: "name",
            render: (name: string) => <div className="font-medium">{name}</div>,
        },
        {
            title: "Ventas",
            dataIndex: "count",
            key: "count",
            render: (count: number) => <Tag color="blue">{count} unidades</Tag>,
        },
        {
            title: "Ingresos",
            dataIndex: "revenue",
            key: "revenue",
            render: (revenue: number) => (
                <div className="flex items-center">
                    <DollarSign size={16} className="text-green-500 mr-1" />
                    <span className="font-semibold">
                        S/. {revenue.toLocaleString()}
                    </span>
                </div>
            ),
        },
        {
            title: "Participación",
            key: "proportion",
            render: (
                _: string,
                record: {
                    revenue: number;
                },
            ) => {
                const percentage =
                    calculatedMaxRevenue > 0
                        ? Math.round((record.revenue / calculatedMaxRevenue) * 100)
                        : 0;

                return (
                    <div className="w-full">
                        <Progress
                            percent={percentage}
                            size="small"
                            showInfo={false}
                            strokeColor={{
                                "0%": "#4096ff",
                                "100%": "#87d068",
                            }}
                        />
                        <div className="text-xs text-right mt-1">{percentage}%</div>
                    </div>
                );
            },
        },
        {
            title: "Tendencia",
            key: "trend",
            render: () => {
                // In a real application, we would use actual trend data
                // Here we're just showing a random trend icon for illustrative purposes
                const isUp = Math.random() > 0.3;
                return (
                    <div
                        className={`flex items-center ${isUp ? "text-green-500" : "text-red-500"}`}
                    >
                        {isUp ? (
                            <ArrowUpRight size={16} />
                        ) : (
                            <ArrowUpRight size={16} className="rotate-90" />
                        )}
                        <span className="ml-1">
                            {isUp ? "+" : "-"}
                            {Math.floor(Math.random() * 30) + 1}%
                        </span>
                    </div>
                );
            },
        },
    ];

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <Table
                dataSource={data.map((item, index) => ({ ...item, key: index }))}
                columns={columns}
                pagination={false}
                size="small"
            />
        </Card>
    );
}
