import { But<PERSON>, Form, InputNumber, Switch } from "antd";
import {
    ExternalLink,
    Mail,
    Phone,
    Save,
    Trash,
    CheckCircle,
    XCircle,
    ToggleLeft,
} from "lucide-react";
import {
    PaymentCurrency,
    PaymentRetrieve,
    PaymentUpdateForm,
} from "../../types/payment";
import UploadVoucher from "../molecules/upload-voucher";
import { Link } from "react-router-dom";
import SelectPaymentMethod from "../molecules/select-payment-method";
import RadioCurrency from "../molecules/radio-currency";
import FormLabel from "../atoms/FormLabel";

type EditPaymentFormProps = {
    payment: PaymentRetrieve;
};

export default function EditPaymentForm({ payment }: EditPaymentFormProps) {
    const { order, voucher } = payment;
    const [form] = Form.useForm<PaymentUpdateForm>();

    const currency = Form.useWatch(["currency"], form);
    const isPaid = Form.useWatch(["isPaid"], form);

    const handleOnChangeIsPaid = (checked: boolean) => {
        if (checked && !voucher) {
            console.log("Please upload a voucher");
        }
    };

    return (
        <>
            <Form
                name="edit-order-form"
                layout="vertical"
                form={form}
                initialValues={{
                    amount: payment.amount,
                    paymentMethod: payment.paymentMethod.pmid,
                    currency: payment.currency,
                    isPaid: payment.isPaid,
                    paymentDate: payment.paymentDate,
                }}
            >
                <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                    <div className="col-span-4 space-y-6">
                        <div className="bg-white-full rounded-lg shadow-sm p-4 space-y-4">
                            <p className="text-gray-400 font-semibold text-sm uppercase">
                                SOBRE LA ORDEN
                            </p>

                            <div className="px-1 rounded-md hover:bg-gray-50 transition-colors">
                                <div className="flex items-center gap-4">
                                    <p className="text-sm font-medium">
                                        {order.owner.fullName}{" "}
                                    </p>
                                    <Link
                                        to={`/crm/orders/${order.oid}`}
                                        className="flex items-center text-blue-600 hover:text-blue-800"
                                    >
                                        <span className="text-sm font-medium mr-1">
                                            #{order.oid.slice(-6)}
                                        </span>
                                        <ExternalLink size={14} />
                                    </Link>
                                </div>

                                <div className="flex gap-2">
                                    {order.owner.email && (
                                        <p className="text-xs text-gray-500 flex items-center mt-1">
                                            <Mail
                                                size={14}
                                                className="h-3.5 w-3.5 mr-1"
                                            />
                                            {order.owner.email}
                                        </p>
                                    )}

                                    <p className="text-xs text-gray-500 flex items-center mt-1">
                                        <Phone size={14} className="h-3.5 w-3.5 mr-1" />
                                        {order.owner.phoneNumber}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white-full rounded-lg shadow-sm p-4 space-y-4">
                            <div className="flex items-center justify-between">
                                <p className="text-gray-400 font-semibold text-sm">
                                    DETALLES DEL PAGO
                                </p>

                                <div className="flex items-center gap-3">
                                    <span
                                        className={`text-sm px-3 py-1 rounded-full flex items-center font-medium ${
                                            isPaid
                                                ? "bg-green-100 text-green-800 border border-green-300"
                                                : "bg-red-100 text-red-800 border border-red-300"
                                        }`}
                                    >
                                        {isPaid ? (
                                            <CheckCircle className="w-4 h-4 mr-1.5" />
                                        ) : (
                                            <XCircle className="w-4 h-4 mr-1.5" />
                                        )}
                                        {isPaid ? "Pagado" : "Pendiente"}
                                    </span>
                                    <Button
                                        type="default"
                                        size="small"
                                        onClick={() =>
                                            form.setFieldValue("isPaid", !isPaid)
                                        }
                                        className="flex items-center text-xs hover:border-blue-500 hover:text-blue-600"
                                        icon={
                                            <ToggleLeft className="w-3.5 h-3.5 mr-1" />
                                        }
                                    >
                                        {isPaid
                                            ? "Marcar como pendiente"
                                            : "Marcar como pagado"}
                                    </Button>
                                </div>
                            </div>
                            <div className="grid grid-cols-4 gap-4">
                                <Form.Item<PaymentUpdateForm>
                                    name="paymentMethod"
                                    label="Método de Pago"
                                    className="col-span-2"
                                >
                                    <SelectPaymentMethod />
                                </Form.Item>
                                <Form.Item<PaymentUpdateForm>
                                    name="currency"
                                    label="Moneda"
                                >
                                    <RadioCurrency block />
                                </Form.Item>
                                <Form.Item<PaymentUpdateForm>
                                    name="amount"
                                    label="Monto"
                                    rules={[
                                        {
                                            required: true,
                                            message: "Por favor ingresa el monto",
                                        },
                                    ]}
                                >
                                    <InputNumber
                                        className="w-full"
                                        prefix={
                                            currency === PaymentCurrency.PEN
                                                ? "S/ "
                                                : "$ "
                                        }
                                    />
                                </Form.Item>
                                <Form.Item<PaymentUpdateForm>
                                    name="isPaid"
                                    label={
                                        <FormLabel>
                                            {isPaid
                                                ? "¿Marcar como no pagado?"
                                                : "¿Marcar como pagado?"}
                                        </FormLabel>
                                    }
                                >
                                    <Switch
                                        onChange={(checked) =>
                                            handleOnChangeIsPaid(checked)
                                        }
                                    />
                                </Form.Item>
                            </div>
                        </div>
                    </div>
                    <div className="col-span-2 space-y-6">
                        <div className="bg-white-full p-5 rounded-lg shadow-sm">
                            <p className="text-gray-400 font-semibold text-sm">
                                ACCIONES
                            </p>
                            <div className="flex flex-col gap-3">
                                <div className="flex gap-3 justify-end">
                                    <Button
                                        type="primary"
                                        size="large"
                                        style={{ fontSize: 16 }}
                                        icon={<Trash />}
                                        danger
                                    >
                                        Eliminar
                                    </Button>
                                    <Button
                                        type="primary"
                                        size="large"
                                        style={{ fontSize: 16 }}
                                        icon={<Save />}
                                        htmlType="submit"
                                    >
                                        Guardar
                                    </Button>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white-full p-5 rounded-lg shadow-sm">
                            <p className="text-gray-400 font-semibold text-sm">
                                VOUCHER
                            </p>
                            <UploadVoucher />
                        </div>
                    </div>
                </div>
            </Form>
        </>
    );
}
