import { Tag, Tooltip } from "antd";
import {
    ArrowRightCircle,
    BookOpen,
    Calendar,
    DollarSign,
    Edit3,
    FileText,
    Mail,
    Trash2,
    GripVertical,
} from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { Order, OrderCurrency, OrderStage } from "@/features/crm/types/order";
import { getCurrencyByPhoneNumber } from "@/features/crm/utils/currency";
import { formatDateTime } from "@lib/helpers";
import { useEffect, useRef } from "react";
import { makeDraggable } from "@lib/drag-and-drop";

interface OrderCardProps {
    order: Order;
}

export default function OrderCard({ order }: OrderCardProps) {
    const navigate = useNavigate();
    const cardRef = useRef<HTMLDivElement>(null);

    const importantDate: string | undefined = (() => {
        const stage = order.stage;
        if (stage === OrderStage.PROSPECT) {
            return order.prospectAt;
        }
        if (stage === OrderStage.INTERESTED) {
            return order.interestedAt;
        }
        if (stage === OrderStage.TO_PAY) {
            return order.toPayAt;
        }
        if (stage === OrderStage.PAID) {
            return order.paidAt;
        }
        if (stage === OrderStage.LOST) {
            return order.lostAt;
        }
        return undefined;
    })();

    const onEdit = (order: Order) => {
        navigate(`/crm/orders/${order.oid}`);
    };

    useEffect(() => {
        const element = cardRef.current;
        if (!element) return;

        return makeDraggable(element, order);
    }, [order]);

    return (
        <div
            ref={cardRef}
            className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all p-3 mb-3 cursor-grab active:cursor-grabbing"
        >
            {/* Drag handle */}
            <div className="flex items-center justify-between mb-2">
                <div className="text-xs text-gray-500 flex items-center">
                    <Calendar size={12} className="mr-1" />
                    <span>{formatDateTime(importantDate)}</span>
                </div>
                <div className="text-gray-400 hover:text-gray-600 cursor-grab">
                    <GripVertical size={14} />
                </div>
            </div>

            {/* Nombre del cliente */}
            <Link to={`/crm/orders/${order.oid}`}>
                <h3 className="font-medium text-gray-800 mb-1 truncate hover:text-blue-600">
                    {order.owner.fullName}
                </h3>
            </Link>

            {/* Programa o curso */}
            <div className="text-sm text-gray-600 mb-2 truncate">
                <BookOpen size={14} className="inline mr-1" />
                {order.orderItems.map((item) => (
                    <span key={item.oiid}>{item.offering.name}</span>
                ))}
            </div>

            {/* Precio */}
            {(() => {
                const currency = getCurrencyByPhoneNumber(order.owner.phoneNumber);
                const symbol = currency === OrderCurrency.USD ? "$" : "S/";
                const colorClass =
                    currency === OrderCurrency.USD
                        ? "bg-blue-50 text-blue-700"
                        : "bg-green-50 text-green-700";

                return (
                    <div className={`flex items-center justify-between mb-3`}>
                        <div
                            className={`${colorClass} px-2 py-1 rounded text-sm font-medium flex items-center`}
                        >
                            <DollarSign size={14} className="mr-1" />
                            <span>
                                {symbol}
                                {order.agreedTotal?.toLocaleString() || "1,500.00"}
                            </span>
                        </div>
                        <Tag color={currency === OrderCurrency.USD ? "blue" : "green"}>
                            {currency.toUpperCase()}
                        </Tag>
                    </div>
                );
            })()}

            {/* Barra de acciones con Tooltip */}
            <div className="flex justify-between pt-2 border-t border-gray-100">
                <Tooltip title="Editar prospecto">
                    <button
                        className="text-gray-500 hover:text-blue-600 p-1"
                        onClick={() => onEdit(order)}
                    >
                        <Edit3 size={16} />
                    </button>
                </Tooltip>

                <Tooltip title="Enviar recordatorio">
                    <button className="text-gray-500 hover:text-green-600 p-1">
                        <Mail size={16} />
                    </button>
                </Tooltip>

                <Tooltip title="Mover a interesados">
                    <button className="text-gray-500 hover:text-purple-600 p-1">
                        <ArrowRightCircle size={16} />
                    </button>
                </Tooltip>

                <Tooltip title="Añadir nota">
                    <button className="text-gray-500 hover:text-yellow-600 p-1">
                        <FileText size={16} />
                    </button>
                </Tooltip>

                <Tooltip title="Eliminar">
                    <button className="text-gray-500 hover:text-red-600 p-1">
                        <Trash2 size={16} />
                    </button>
                </Tooltip>
            </div>
        </div>
    );
}
