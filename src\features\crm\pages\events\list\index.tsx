import CrmLayout from "@/features/crm/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import CreateEventForm from "@/features/crm/components/organisms/create-event-form";

import { Badge, Button, Drawer, Input, Modal, Pagination, Typography } from "antd";
import { Plus, SlidersHorizontal } from "lucide-react";
import { useState } from "react";
import { useEvents } from "@/features/crm/hooks/use-event";
import EventsTable from "@/features/crm/components/organisms/events-table";
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    ListEventQueryParams,
} from "@/features/crm/types/event";
import { useSearchParams } from "react-router-dom";

const { Text } = Typography;
const { Search } = Input;

export default function EventsListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const page = Number(searchParams.get("page")) || DEFAULT_PAGE;
    const pageSize = Number(searchParams.get("pageSize")) || DEFAULT_PAGE_SIZE;
    const search = searchParams.get("search") || undefined;

    const queryParams: ListEventQueryParams = {
        page,
        pageSize,
        search,
    };

    const { events, count } = useEvents({ queryParams });
    const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState<boolean>(false);

    const [isCreateEventModalOpen, setIsCreateEventModalOpen] =
        useState<boolean>(false);
    const handleCreateEventModalOpen = () => {
        setIsCreateEventModalOpen(true);
    };
    const handleCreateEventModalClose = () => {
        setIsCreateEventModalOpen(false);
    };

    const [activeTag, setActiveTag] = useState("Todos");
    const tags = ["Todos", "Culminados", "Planeados"];

    const handleSetPage = (page: number, pageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", page.toString());
            prev.set("pageSize", pageSize.toString());
            return prev;
        });
    };

    const handleSetSearchQuery = (value: string) => {
        setSearchParams((prev) => {
            prev.set("search", value);
            return prev;
        });
    };

    return (
        <CrmLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí los pagos y deudas de CEU" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Plus />}
                            onClick={() => {
                                handleCreateEventModalOpen();
                            }}
                        >
                            Agregar
                        </Button>
                        <Modal
                            centered
                            open={isCreateEventModalOpen}
                            onCancel={() => {
                                handleCreateEventModalClose();
                            }}
                            footer={false}
                            title="Agregar nuevo evento"
                            width={600}
                        >
                            <CreateEventForm closeModal={handleCreateEventModalClose} />
                        </Modal>
                    </div>
                </div>
                <div className="p-5 bg-white-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center">
                        <Text className="text-black-medium text-2xl font-semibold">
                            Eventos <Badge count={count} color="blue" size="default" />
                        </Text>
                        <Search
                            size="large"
                            placeholder="Buscar por nombre del evento, descripción o programa"
                            onSearch={(value) => {
                                handleSetSearchQuery(value);
                            }}
                            enterButton={true}
                            allowClear
                            className="max-w-screen-sm"
                        />
                        <div className="flex items-center gap-3">
                            <Button
                                icon={<SlidersHorizontal size={16} />}
                                onClick={() => setIsFilterDrawerOpen(true)}
                            >
                                Filtros
                            </Button>
                            <Drawer
                                title="Aplicar filtros"
                                placement="right"
                                closable={true}
                                onClose={() => setIsFilterDrawerOpen(false)}
                                open={isFilterDrawerOpen}
                            ></Drawer>
                        </div>
                    </div>
                </div>

                <div className="items-center gap-2 p-3 bg-gray-50 rounded-lg shadow-sm hidden">
                    {tags.map((tag) => (
                        <button
                            key={tag}
                            onClick={() => setActiveTag(tag)}
                            className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 ${
                                activeTag === tag
                                    ? "bg-blue-500 text-white-full shadow-md"
                                    : "bg-white text-gray-600 hover:bg-gray-100"
                            }`}
                        >
                            {tag}
                        </button>
                    ))}
                </div>

                <EventsTable events={events} />
                <div className="flex justify-between items-center p-4 bg-white-full rounded-lg shadow-sm">
                    <Text type="secondary">
                        {events.length} de {count} órdenes
                    </Text>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={count}
                        onChange={handleSetPage}
                        showSizeChanger
                    />
                </div>
            </div>
        </CrmLayout>
    );
}
