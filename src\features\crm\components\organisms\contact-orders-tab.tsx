import { useOrdersByContact } from "@/features/crm/hooks/use-order";
import ContactOrdersTable from "./contact-orders-table";
import Spinner from "@components/shared/atoms/Spinner";
import { Input, Typography } from "antd";
import { useState, useMemo } from "react";
import { OrderStage, OrderStageLabels } from "@/features/crm/types/order";
import { Search } from "lucide-react";

const { Text } = Typography;

const STAGE_FILTER_TAGS = [
    {
        value: "all",
        label: "Todas",
        color: "default",
    },
    {
        value: OrderStage.PROSPECT,
        label: OrderStageLabels[OrderStage.PROSPECT],
        color: "blue",
    },
    {
        value: OrderStage.INTERESTED,
        label: OrderStageLabels[OrderStage.INTERESTED],
        color: "green",
    },
    {
        value: OrderStage.TO_PAY,
        label: OrderStageLabels[OrderStage.TO_PAY],
        color: "orange",
    },
    {
        value: OrderStage.PAID,
        label: OrderStageLabels[OrderStage.PAID],
        color: "purple",
    },
    {
        value: OrderStage.LOST,
        label: OrderStageLabels[OrderStage.LOST],
        color: "red",
    },
];

type ContactOrdersTabProps = {
    contactId: string;
};

export default function ContactOrdersTab({ contactId }: ContactOrdersTabProps) {
    const { orders: allOrders, isLoading, count } = useOrdersByContact(contactId);
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedStage, setSelectedStage] = useState("all");

    const filteredOrders = useMemo(() => {
        let filtered = allOrders;

        // Filtrar por estado
        if (selectedStage !== "all") {
            filtered = filtered.filter((order) => order.stage === selectedStage);
        }

        // Filtrar por término de búsqueda
        if (searchTerm) {
            const searchLower = searchTerm.toLowerCase();
            filtered = filtered.filter(
                (order) =>
                    order.oid.toLowerCase().includes(searchLower) ||
                    order.orderItems?.some((item) =>
                        item.offering.name.toLowerCase().includes(searchLower),
                    ) ||
                    OrderStageLabels[order.stage].toLowerCase().includes(searchLower),
            );
        }

        return filtered;
    }, [allOrders, selectedStage, searchTerm]);

    if (isLoading) {
        return <Spinner />;
    }

    return (
        <div className="space-y-6">
            {/* Header con estadísticas */}
            <div className="flex justify-between items-center">
                <div>
                    <h3 className="text-xl font-semibold text-gray-800">
                        Órdenes del contacto
                    </h3>
                    <Text type="secondary">
                        {filteredOrders.length} de {count} órdenes
                    </Text>
                </div>
            </div>

            {/* Controles de filtrado */}
            <div className="bg-white p-4 rounded-lg shadow-sm space-y-4">
                <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                    <Input
                        prefix={<Search size={16} />}
                        placeholder="Buscar por ID, programa o estado..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="max-w-md"
                        allowClear
                    />
                </div>

                {/* Filtros por estado */}
                <div className="flex flex-wrap gap-2">
                    {STAGE_FILTER_TAGS.map((tag) => (
                        <button
                            key={tag.value}
                            onClick={() => setSelectedStage(tag.value)}
                            className={`px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 ${
                                selectedStage === tag.value
                                    ? "bg-blue-500 text-white shadow-md"
                                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                            }`}
                        >
                            {tag.label}
                            {tag.value !== "all" && (
                                <span className="ml-2 bg-white bg-opacity-20 px-2 py-0.5 rounded-full text-xs">
                                    {
                                        allOrders.filter(
                                            (order) => order.stage === tag.value,
                                        ).length
                                    }
                                </span>
                            )}
                        </button>
                    ))}
                </div>
            </div>

            {/* Tabla de órdenes */}
            <ContactOrdersTable orders={filteredOrders} loading={isLoading} />
        </div>
    );
}
