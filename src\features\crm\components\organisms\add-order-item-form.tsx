import { Form, InputNumber, Typography, Switch } from "antd";
import SelectOffering from "../molecules/select-offering";
import { useState } from "react";

const { Text } = Typography;

type AddOrderItemFormValues = {
    offering: string;
    quantity: number;
    customPrice?: number;
};

export default function AddOrderItemForm() {
    const [form] = Form.useForm();
    const [useCustomPrice, setUseCustomPrice] = useState(false);

    return (
        <Form
            form={form}
            layout="vertical"
            initialValues={{
                offering: "",
                quantity: 1,
            }}
        >
            <Form.Item<AddOrderItemFormValues>
                label="Nombre del producto"
                name="offering"
                rules={[
                    { required: true, message: "Por favor selecciona un producto" },
                ]}
            >
                <SelectOffering />
            </Form.Item>
            <div className="grid grid-cols-2">
                <Form.Item<AddOrderItemFormValues>
                    className="w-full"
                    label="Cantidad"
                    name="quantity"
                    rules={[
                        { required: true, message: "Por favor ingresa una cantidad" },
                    ]}
                >
                    <InputNumber min={1} />
                </Form.Item>
                <div className="flex justify-end py-2 gap-2 items-center">
                    <Text type="secondary">¿Usar precio por defecto?</Text>
                    <Switch
                        size="small"
                        checked={useCustomPrice}
                        onChange={setUseCustomPrice}
                    />
                </div>
                {useCustomPrice && (
                    <div className="flex grid-cols-2">
                        <Form.Item<AddOrderItemFormValues>
                            className="w-full"
                            label="Precio personalizado"
                            name="customPrice"
                        >
                            <InputNumber min={0} prefix="$" />
                        </Form.Item>
                    </div>
                )}
            </div>
        </Form>
    );
}
