import { Form, InputNumber, Typography, Switch } from "antd";
import SelectOffering from "../molecules/select-offering";
import { useState } from "react";

const { Text } = Typography;

type AddOrderItemFormValues = {
    offering: string;
    customPrice?: number;
};

export default function AddOrderItemForm() {
    const [form] = Form.useForm();
    const [useCustomPrice, setUseCustomPrice] = useState(false);

    return (
        <Form
            form={form}
            layout="vertical"
            initialValues={{
                offering: "",
                quantity: 1,
            }}
        >
            <Form.Item<AddOrderItemFormValues>
                label="Nombre del producto"
                name="offering"
                rules={[
                    { required: true, message: "Por favor selecciona un producto" },
                ]}
            >
                <SelectOffering />
            </Form.Item>
            <div className="grid grid-cols-2">
                <div className="flex py-2 gap-2 items-center">
                    <Text type="secondary">¿Usar precio personalizado?</Text>
                    <Switch
                        size="small"
                        checked={useCustomPrice}
                        onChange={setUseCustomPrice}
                    />
                </div>
                {useCustomPrice && (
                    <Form.Item<AddOrderItemFormValues>
                        className="w-full"
                        rules={[
                            {
                                required: true,
                                message: "Por favor ingresa un precio",
                            },
                        ]}
                        label="Precio personalizado"
                        name="customPrice"
                    >
                        <InputNumber className="w-full" min={0} prefix="$" />
                    </Form.Item>
                )}
            </div>
        </Form>
    );
}
