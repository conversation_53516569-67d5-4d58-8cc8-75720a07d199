import { App, DraggerProps, Typography, Upload, UploadFile } from "antd";
import { CloudUpload } from "lucide-react";
import { removeVoucher, uploadVoucher } from "../../services/portals/payment";
import { RcFile } from "antd/es/upload";

const { Dragger } = Upload;
const { Text } = Typography;

interface UploadVoucherProps {
    value?: UploadFile[];
    onChange?: (fileList: UploadFile[]) => void;
    maxCount?: number;
    multiple?: boolean;
}

export default function UploadVoucher({
    value,
    onChange,
    maxCount = 1,
    multiple = false,
}: UploadVoucherProps) {
    const { notification } = App.useApp();

    const draggerProps: DraggerProps = {
        customRequest: async (options) => {
            const { file, onSuccess, onError } = options;
            try {
                // Define proper types for file
                const fileToUpload = file as RcFile & Partial<UploadFile>;
                const fileRes = await uploadVoucher(fileToUpload);
                if (fileRes && onSuccess) {
                    // Update the file's uid to the fid returned by uploadVoucher
                    const updatedFile = { ...fileToUpload, uid: fileRes.fid };
                    onSuccess(fileRes, updatedFile);
                }
            } catch (error) {
                if (onError)
                    onError(
                        new Error(
                            error instanceof Error ? error.message : "Unknown error",
                        ),
                        file,
                    );
            }
        },
        onChange(info) {
            if (onChange) {
                onChange(info.fileList);
            }
        },
        multiple,
        maxCount,
        accept: ".pdf, .jpg, .jpeg, .png",
        beforeUpload: (file) => {
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isLt2M) {
                notification.error({
                    message: "El archivo debe ser menor a 2MB",
                    duration: 2,
                });
            }
            return isLt2M;
        },
        onRemove: async (file) => {
            const res = await removeVoucher(file.uid);
            if (res) {
                notification.success({
                    message: "Archivo eliminado correctamente",
                    duration: 2,
                });
            } else {
                notification.error({
                    message: "Error al eliminar el archivo",
                    duration: 2,
                });
            }
        },
        showUploadList: {
            showDownloadIcon: true,
            showRemoveIcon: true,
        },
        fileList: value,
    };

    return (
        <Dragger {...draggerProps}>
            <div className="flex flex-col justify-center items-center">
                <CloudUpload />
                <Text className="font-medium text-black-full">
                    Arrastre una imagen o PDF aquí
                </Text>
                <Text className="text-xs text-black-medium">
                    Solo una imagen o PDF (Máx. 10MB)
                </Text>
            </div>
        </Dragger>
    );
}
