import { useContacts } from "@/features/crm/hooks/use-contact";
import { <PERSON><PERSON>, Divide<PERSON>, Modal, Select, SelectProps } from "antd";
import { ExternalLink, Plus } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import CreateContactForm from "@/features/crm/components/organisms/create-contact-form";

interface SelectContactProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
}

export default function SelectContact({
    value,
    onChange,
    ...restProps
}: SelectContactProps) {
    const [modalOpen, setModalOpen] = useState(false);
    const { contacts, isLoading } = useContacts({
        page: 1,
        pageSize: 1000,
    });
    const contactsOptions: SelectProps["options"] = contacts.map((contact) => ({
        value: contact.uid,
        label: contact.fullName,
        data: {
            info: contact,
        },
    }));

    const handleCloseModal = () => {
        setModalOpen(false);
    };

    return (
        <>
            <Modal
                title={
                    <div className="text-lg font-semibold text-center">
                        Crear Contacto
                    </div>
                }
                footer={false}
                open={modalOpen}
                centered
                onCancel={() => {
                    setModalOpen(false);
                }}
            >
                <CreateContactForm handleCloseModal={handleCloseModal} />
            </Modal>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                options={contactsOptions}
                optionRender={(option) => (
                    <div className="flex justify-between items-center">
                        <span>{option.data.label}</span>
                        <span className="text-xs text-gray-600">
                            {option.data.data.info.phoneNumber}
                        </span>
                        <Link
                            to={`/crm/contacts/${option.data.value}`}
                            title="View Contact"
                        >
                            <ExternalLink size={14} />
                        </Link>
                    </div>
                )}
                loading={isLoading}
                optionFilterProp="label"
                allowClear
                showSearch
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        <div className="flex justify-between items-center px-2">
                            <p className="text-sm text-gray-700 font-medium">
                                ¿No encuentras el contacto?
                            </p>
                            <Button
                                size="small"
                                type="primary"
                                icon={<Plus size={12} />}
                                onClick={() => setModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </>
                )}
            />
        </>
    );
}
