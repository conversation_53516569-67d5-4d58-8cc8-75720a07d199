import GeneralContactDetail from "@/features/crm/components/organisms/GeneralContactDetail";
import ContactOrdersTab from "@/features/crm/components/organisms/contact-orders-tab";
import Spinner from "@components/shared/atoms/Spinner";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import CrmLayout from "@/features/crm/layout";
import { retrieveContact } from "@/features/crm/services/portals/contact";
import { useQuery } from "@tanstack/react-query";
import { Breadcrumb, Tabs, TabsProps, Typography } from "antd";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useEffect } from "react";

const { Text } = Typography;

type ContactParams = {
    cid: string;
};

export default function ContactDetailPage() {
    const { cid } = useParams<ContactParams>();
    const navigate = useNavigate();
    const {
        data: contact,
        isLoading,
        isError,
    } = useQuery({
        queryKey: ["contacts", cid],
        queryFn: async () => retrieveContact(cid as string),
        enabled: !!cid,
        refetchOnWindowFocus: false,
        retry: false,
    });

    useEffect(() => {
        if (isError) {
            navigate("/crm/contacts");
        }
    }, [isError, navigate]);

    const tabItems: TabsProps["items"] = [
        {
            key: "general",
            label: "General",
            children: contact && <GeneralContactDetail contact={contact} />,
        },
        {
            key: "orders",
            label: "Órdenes",
            children: contact && <ContactOrdersTab contactId={contact.uid} />,
        },
        {
            key: "payments",
            label: "Pagos",
            children: <></>,
            disabled: true,
        },
    ];
    return (
        <CrmLayout>
            <div className="max-w-7xl w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Edita aquí los detalles de un Contacto." />
                </div>
                {isLoading ? (
                    <Spinner />
                ) : (
                    <>
                        <div className="p-5 bg-white-full rounded-lg space-y-5 shadow-sm">
                            <div className="flex items-center">
                                <Breadcrumb
                                    separator=">"
                                    items={[
                                        {
                                            title: (
                                                <Link
                                                    to="/crm/contacts"
                                                    className="text-base"
                                                >
                                                    Contactos
                                                </Link>
                                            ),
                                        },
                                        {
                                            title: (
                                                <Link
                                                    to={`/crm/contacts/${contact?.uid}`}
                                                    className="text-base"
                                                >
                                                    {contact?.fullName ||
                                                        contact?.phoneNumber ||
                                                        "Sin nombre"}
                                                </Link>
                                            ),
                                        },
                                        {
                                            title: (
                                                <Text className="text-base">
                                                    Ver & Editar
                                                </Text>
                                            ),
                                        },
                                    ]}
                                />
                            </div>
                        </div>
                        <Tabs items={tabItems} />
                    </>
                )}
            </div>
        </CrmLayout>
    );
}
