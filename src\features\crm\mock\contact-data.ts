import { ContactListItem, ContactOcupation } from "../types/contact";
import dayjs from "dayjs";
import "dayjs/locale/es";

// Mock data for contacts
export const mockContacts: ContactListItem[] = [
    {
        uid: "1",
        key: "1",
        username: "analopez",
        fullName: "<PERSON>",
        isActive: true,
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "<EMAIL>",
        phoneNumber: "+34600111222",
        ocupation: ContactOcupation.STUDENT,
        isStaff: false,
        major: "Administración de Empresas",
        term: "Tercer año",
        country: "España",
        university: "Universidad Complutense de Madrid",
        createdAt: "2024-02-15T10:30:00Z",
        updatedAt: "2024-05-01T14:20:00Z",
    },
    {
        uid: "2",
        key: "2",
        username: "carlos<PERSON>ente<PERSON>",
        fullName: "<PERSON>",
        isActive: true,
        firstName: "<PERSON>",
        lastName: "Fuente<PERSON>",
        email: "<EMAIL>",
        phoneNumber: "+34600333444",
        ocupation: ContactOcupation.EMPLOYEE,
        company: "Telefónica",
        role: "Analista de Datos",
        isStaff: false,
        country: "España",
        createdAt: "2024-01-10T09:15:00Z",
        updatedAt: "2024-04-20T11:30:00Z",
    },
    {
        uid: "3",
        key: "3",
        username: "mariarodriguez",
        fullName: "María Rodríguez",
        isActive: true,
        firstName: "María",
        lastName: "Rodríguez",
        email: "<EMAIL>",
        phoneNumber: "+34600555666",
        ocupation: ContactOcupation.STUDENT,
        isStaff: false,
        major: "Ingeniería Informática",
        term: "Segundo año",
        country: "México",
        university: "Universidad Politécnica de Madrid",
        createdAt: "2024-03-05T14:45:00Z",
        updatedAt: "2024-05-10T16:20:00Z",
    },
    {
        uid: "4",
        key: "4",
        username: "pablogomez",
        fullName: "Pablo Gómez",
        isActive: false,
        firstName: "Pablo",
        lastName: "Gómez",
        email: "<EMAIL>",
        phoneNumber: "+34600777888",
        ocupation: ContactOcupation.INDEPENDENT,
        role: "Consultor",
        isStaff: false,
        country: "Argentina",
        createdAt: "2024-01-20T11:00:00Z",
        updatedAt: "2024-03-15T13:40:00Z",
    },
    {
        uid: "5",
        key: "5",
        username: "lauramartin",
        fullName: "Laura Martín",
        isActive: true,
        firstName: "Laura",
        lastName: "Martín",
        email: "<EMAIL>",
        phoneNumber: "+34600999000",
        ocupation: ContactOcupation.EMPLOYEE,
        company: "BBVA",
        role: "Project Manager",
        isStaff: false,
        country: "España",
        createdAt: "2024-02-28T08:30:00Z",
        updatedAt: "2024-05-05T10:15:00Z",
    },
    {
        uid: "6",
        key: "6",
        username: "danielrivera",
        fullName: "Daniel Rivera",
        isActive: true,
        firstName: "Daniel",
        lastName: "Rivera",
        email: "<EMAIL>",
        phoneNumber: "+34601111222",
        ocupation: ContactOcupation.STUDENT,
        isStaff: false,
        major: "Marketing Digital",
        term: "Primer año",
        country: "Colombia",
        university: "Universidad Autónoma de Madrid",
        createdAt: "2024-04-10T13:20:00Z",
        updatedAt: "2024-05-12T09:45:00Z",
    },
    {
        uid: "7",
        key: "7",
        username: "sofiacastro",
        fullName: "Sofía Castro",
        isActive: false,
        firstName: "Sofía",
        lastName: "Castro",
        email: "<EMAIL>",
        phoneNumber: "+34601333444",
        ocupation: ContactOcupation.INDEPENDENT,
        role: "Diseñadora Gráfica",
        isStaff: false,
        createdAt: "2024-01-05T15:10:00Z",
        updatedAt: "2024-02-20T17:30:00Z",
    },
    {
        uid: "8",
        key: "8",
        username: "javiermolina",
        fullName: "Javier Molina",
        isActive: true,
        firstName: "Javier",
        lastName: "Molina",
        email: "<EMAIL>",
        phoneNumber: "+34601555666",
        ocupation: ContactOcupation.EMPLOYEE,
        company: "Santander",
        role: "Analista Financiero",
        isStaff: false,
        country: "España",
        createdAt: "2024-03-15T10:00:00Z",
        updatedAt: "2024-04-25T12:20:00Z",
    },
    {
        uid: "9",
        key: "9",
        username: "carmenmendez",
        fullName: "Carmen Méndez",
        isActive: true,
        firstName: "Carmen",
        lastName: "Méndez",
        email: "<EMAIL>",
        phoneNumber: "+34601777888",
        ocupation: ContactOcupation.STUDENT,
        isStaff: false,
        major: "Derecho",
        term: "Cuarto año",
        country: "Perú",
        university: "Universidad Carlos III de Madrid",
        createdAt: "2024-02-10T09:30:00Z",
        updatedAt: "2024-05-08T11:15:00Z",
    },
    {
        uid: "10",
        key: "10",
        username: "sergiogonzalez",
        fullName: "Sergio González",
        isActive: true,
        firstName: "Sergio",
        lastName: "González",
        email: "<EMAIL>",
        phoneNumber: "+34601999000",
        ocupation: ContactOcupation.INDEPENDENT,
        role: "Desarrollador Web",
        isStaff: false,
        createdAt: "2024-04-01T14:00:00Z",
        updatedAt: "2024-05-15T16:45:00Z",
    },
    {
        uid: "11",
        key: "11",
        username: "luciaperez",
        fullName: "Lucía Pérez",
        isActive: true,
        firstName: "Lucía",
        lastName: "Pérez",
        email: "<EMAIL>",
        phoneNumber: "+34602111222",
        ocupation: ContactOcupation.EMPLOYEE,
        company: "Iberdrola",
        role: "Recursos Humanos",
        isStaff: false,
        createdAt: "2024-01-25T13:45:00Z",
        updatedAt: "2024-04-15T15:30:00Z",
    },
    {
        uid: "12",
        key: "12",
        username: "alejandronavarro",
        fullName: "Alejandro Navarro",
        isActive: true,
        firstName: "Alejandro",
        lastName: "Navarro",
        email: "<EMAIL>",
        phoneNumber: "+34602333444",
        ocupation: ContactOcupation.STUDENT,
        isStaff: false,
        major: "Psicología",
        term: "Segundo año",
        country: "Chile",
        university: "Universidad Complutense de Madrid",
        createdAt: "2024-03-20T11:25:00Z",
        updatedAt: "2024-05-05T13:10:00Z",
    },
    {
        uid: "13",
        key: "13",
        username: "martinaortiz",
        fullName: "Martina Ortiz",
        isActive: false,
        firstName: "Martina",
        lastName: "Ortiz",
        email: "<EMAIL>",
        phoneNumber: "+34602555666",
        ocupation: ContactOcupation.INDEPENDENT,
        role: "Fotógrafa",
        isStaff: false,
        createdAt: "2024-01-15T10:40:00Z",
        updatedAt: "2024-03-10T12:25:00Z",
    },
    {
        uid: "14",
        key: "14",
        username: "nicolasruiz",
        fullName: "Nicolás Ruiz",
        isActive: true,
        firstName: "Nicolás",
        lastName: "Ruiz",
        email: "<EMAIL>",
        phoneNumber: "+34602777888",
        ocupation: ContactOcupation.EMPLOYEE,
        company: "Endesa",
        role: "Ingeniero",
        isStaff: false,
        createdAt: "2024-02-05T09:50:00Z",
        updatedAt: "2024-04-20T11:35:00Z",
    },
    {
        uid: "15",
        key: "15",
        username: "isabelromero",
        fullName: "Isabel Romero",
        isActive: true,
        firstName: "Isabel",
        lastName: "Romero",
        email: "<EMAIL>",
        phoneNumber: "+34602999000",
        ocupation: ContactOcupation.STUDENT,
        isStaff: false,
        major: "Ciencias Ambientales",
        term: "Tercer año",
        country: "Ecuador",
        university: "Universidad Politécnica de Madrid",
        createdAt: "2024-03-25T14:15:00Z",
        updatedAt: "2024-05-10T16:00:00Z",
    },
    {
        uid: "16",
        key: "16",
        username: "diegovargas",
        fullName: "Diego Vargas",
        isActive: true,
        firstName: "Diego",
        lastName: "Vargas",
        email: "<EMAIL>",
        phoneNumber: "+34603111222",
        ocupation: ContactOcupation.EMPLOYEE,
        company: "Repsol",
        role: "Técnico de Sistemas",
        isStaff: false,
        createdAt: "2024-01-30T12:00:00Z",
        updatedAt: "2024-04-28T10:45:00Z",
    },
    {
        uid: "17",
        key: "17",
        username: "patriciasoto",
        fullName: "Patricia Soto",
        isActive: true,
        firstName: "Patricia",
        lastName: "Soto",
        email: "<EMAIL>",
        phoneNumber: "+34603333444",
        ocupation: ContactOcupation.STUDENT,
        isStaff: false,
        major: "Medicina",
        term: "Quinto año",
        country: "Venezuela",
        university: "Universidad Autónoma de Madrid",
        createdAt: "2024-02-20T15:30:00Z",
        updatedAt: "2024-05-18T09:20:00Z",
    },
    {
        uid: "18",
        key: "18",
        username: "fernandovega",
        fullName: "Fernando Vega",
        isActive: false,
        firstName: "Fernando",
        lastName: "Vega",
        email: "<EMAIL>",
        phoneNumber: "+34603555666",
        ocupation: ContactOcupation.INDEPENDENT,
        role: "Arquitecto",
        isStaff: false,
        createdAt: "2024-01-08T11:15:00Z",
        updatedAt: "2024-03-22T14:30:00Z",
    },
    {
        uid: "19",
        key: "19",
        username: "andrealuna",
        fullName: "Andrea Luna",
        isActive: true,
        firstName: "Andrea",
        lastName: "Luna",
        email: "<EMAIL>",
        phoneNumber: "+34603777888",
        ocupation: ContactOcupation.EMPLOYEE,
        company: "Mapfre",
        role: "Agente de Seguros",
        isStaff: false,
        createdAt: "2024-03-12T13:45:00Z",
        updatedAt: "2024-05-02T16:10:00Z",
    },
    {
        uid: "20",
        key: "20",
        username: "ricardoherrera",
        fullName: "Ricardo Herrera",
        isActive: true,
        firstName: "Ricardo",
        lastName: "Herrera",
        email: "<EMAIL>",
        phoneNumber: "+34603999000",
        ocupation: ContactOcupation.STUDENT,
        isStaff: false,
        major: "Ingeniería Civil",
        term: "Cuarto año",
        country: "Brasil",
        university: "Universidad Carlos III de Madrid",
        createdAt: "2024-04-05T10:20:00Z",
        updatedAt: "2024-05-20T12:35:00Z",
    },
    {
        uid: "21",
        key: "21",
        username: "valeriamendoza",
        fullName: "Valeria Mendoza",
        isActive: true,
        firstName: "Valeria",
        lastName: "Mendoza",
        email: "<EMAIL>",
        phoneNumber: "+34604111222",
        ocupation: ContactOcupation.INDEPENDENT,
        role: "Diseñadora UX/UI",
        isStaff: false,
        createdAt: "2024-02-14T09:00:00Z",
        updatedAt: "2024-04-30T11:25:00Z",
    },
    {
        uid: "22",
        key: "22",
        username: "hectormarin",
        fullName: "Héctor Marín",
        isActive: true,
        firstName: "Héctor",
        lastName: "Marín",
        email: "<EMAIL>",
        phoneNumber: "+34604333444",
        ocupation: ContactOcupation.EMPLOYEE,
        company: "El Corte Inglés",
        role: "Jefe de Ventas",
        isStaff: false,
        createdAt: "2024-01-18T14:10:00Z",
        updatedAt: "2024-05-07T13:55:00Z",
    },
    {
        uid: "23",
        key: "23",
        username: "camilarojas",
        fullName: "Camila Rojas",
        isActive: false,
        firstName: "Camila",
        lastName: "Rojas",
        email: "<EMAIL>",
        phoneNumber: "+34604555666",
        ocupation: ContactOcupation.STUDENT,
        isStaff: false,
        major: "Comunicación Audiovisual",
        term: "Primer año",
        createdAt: "2024-03-08T16:25:00Z",
        updatedAt: "2024-04-12T10:40:00Z",
    },
    {
        uid: "24",
        key: "24",
        username: "albertojimenez",
        fullName: "Alberto Jiménez",
        isActive: true,
        firstName: "Alberto",
        lastName: "Jiménez",
        email: "<EMAIL>",
        phoneNumber: "+34604777888",
        ocupation: ContactOcupation.EMPLOYEE,
        company: "Indra",
        role: "Desarrollador Senior",
        isStaff: false,
        createdAt: "2024-01-22T08:45:00Z",
        updatedAt: "2024-05-14T15:20:00Z",
    },
    {
        uid: "25",
        key: "25",
        username: "noeliacastillo",
        fullName: "Noelia Castillo",
        isActive: true,
        firstName: "Noelia",
        lastName: "Castillo",
        email: "<EMAIL>",
        phoneNumber: "+34604999000",
        ocupation: ContactOcupation.INDEPENDENT,
        role: "Traductora",
        isStaff: false,
        createdAt: "2024-04-18T12:30:00Z",
        updatedAt: "2024-05-22T14:15:00Z",
    },
];

// Statistics for charts
export const contactsByOcupation = [
    {
        name: "Estudiantes",
        value: mockContacts.filter((c) => c.ocupation === ContactOcupation.STUDENT)
            .length,
    },
    {
        name: "Empleados",
        value: mockContacts.filter((c) => c.ocupation === ContactOcupation.EMPLOYEE)
            .length,
    },
    {
        name: "Independientes",
        value: mockContacts.filter((c) => c.ocupation === ContactOcupation.INDEPENDENT)
            .length,
    },
];

const last10Months = Array.from({ length: 10 }).map((_, idx) => {
    const date = dayjs().subtract(18 - idx, "month"); // cambiar a 9 - idx -> últimos 10 meses
    return {
        key: date.format("YYYY-MM"),
        name: date.locale("es").format("MMMM"),
    };
});

export const contactsCreationByMonth = last10Months.map(({ key, name }) => ({
    name: name.charAt(0).toUpperCase() + name.slice(1),
    contacts: mockContacts.filter((c) => c.createdAt.startsWith(key)).length * 10,
}));

export const contactsWithAccumulatedByMonth = contactsCreationByMonth.map(
    (curr, idx, arr) => {
        const totalAccumulated = arr
            .slice(0, idx + 1)
            .reduce((sum, m) => sum + m.contacts, 0);
        return { ...curr, totalAccumulated };
    },
);

export const contactsByStatus = [
    { name: "Activos", value: mockContacts.filter((c) => c.isActive).length },
    { name: "Inactivos", value: mockContacts.filter((c) => !c.isActive).length },
];

export const studentsByMajor = mockContacts
    .filter((c) => c.ocupation === ContactOcupation.STUDENT && c.major)
    .reduce((acc: Record<string, number>, contact) => {
        if (contact.major) {
            acc[contact.major] = (acc[contact.major] || 0) + 1;
        }
        return acc;
    }, {});

export const studentsByMajorChart = Object.entries(studentsByMajor).map(
    ([name, value]) => ({ name, value }),
);

export const employeesByCompany = mockContacts
    .filter((c) => c.ocupation === ContactOcupation.EMPLOYEE && c.company)
    .reduce((acc: Record<string, number>, contact) => {
        if (contact.company) {
            acc[contact.company] = (acc[contact.company] || 0) + 1;
        }
        return acc;
    }, {});

export const employeesByCompanyChart = Object.entries(employeesByCompany).map(
    ([name, value]) => ({ name, value }),
);

// Recent activity mock data
// Segmentación por países
export const contactsByCountry = mockContacts
    .filter((c) => c.country)
    .reduce((acc: Record<string, number>, contact) => {
        if (contact.country) {
            acc[contact.country] = (acc[contact.country] || 0) + 1;
        }
        return acc;
    }, {});

export const contactsByCountryChart = Object.entries(contactsByCountry).map(
    ([name, value]) => ({ name, value }),
);

// Segmentación por universidades (solo estudiantes)
export const studentsByUniversity = mockContacts
    .filter((c) => c.ocupation === ContactOcupation.STUDENT && c.university)
    .reduce((acc: Record<string, number>, contact) => {
        if (contact.university) {
            acc[contact.university] = (acc[contact.university] || 0) + 1;
        }
        return acc;
    }, {});

export const studentsByUniversityChart = Object.entries(studentsByUniversity).map(
    ([name, value]) => ({ name, value }),
);

// Segmentación por año académico (solo estudiantes)
export const studentsByTerm = mockContacts
    .filter((c) => c.ocupation === ContactOcupation.STUDENT && c.term)
    .reduce((acc: Record<string, number>, contact) => {
        if (contact.term) {
            acc[contact.term] = (acc[contact.term] || 0) + 1;
        }
        return acc;
    }, {});

export const studentsByTermChart = Object.entries(studentsByTerm).map(
    ([name, value]) => ({ name, value }),
);

export const recentActivity = [
    {
        id: 1,
        contact: "Ana López",
        action: "Actualización de perfil",
        date: "2024-05-01T14:20:00Z",
    },
    {
        id: 2,
        contact: "María Rodríguez",
        action: "Actualización de perfil",
        date: "2024-05-10T16:20:00Z",
    },
    {
        id: 3,
        contact: "Laura Martín",
        action: "Actualización de perfil",
        date: "2024-05-05T10:15:00Z",
    },
    {
        id: 4,
        contact: "Daniel Rivera",
        action: "Nuevo contacto",
        date: "2024-04-10T13:20:00Z",
    },
    {
        id: 5,
        contact: "Sergio González",
        action: "Actualización de perfil",
        date: "2024-05-15T16:45:00Z",
    },
];
