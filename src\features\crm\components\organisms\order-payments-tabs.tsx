import { usePayments } from "../../hooks/use-payment";
import { RetrieveOrder } from "../../types/order";
import { ListPaymentsQueryParams } from "../../types/payment";
import PaymentsTable from "./payments-table";

export default function OrderPaymentsTabs({ order }: { order: RetrieveOrder }) {
    const queryParams: ListPaymentsQueryParams = {
        order: order.oid,
    };
    const { payments } = usePayments({ queryParams });

    return (
        <>
            <PaymentsTable initialData={payments} />
        </>
    );
}
