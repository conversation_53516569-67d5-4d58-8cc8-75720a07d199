import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    ListPaymentsQueryParams,
    PaymentCreateRequest,
    PaymentListItem,
    PaymentRetrieve,
} from "../../types/payment";
import { UploadFile } from "antd";

export const listPayments = async (
    queryParams: ListPaymentsQueryParams = {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
    },
): Promise<PaginatedResponse<PaymentListItem>> => {
    const response = await portalsApi.get("crm/payments", {
        params: queryParams,
    });
    return response.data;
};

export const retrievePayment = async (pid: string): Promise<PaymentRetrieve> => {
    const response = await portalsApi.get(`crm/payments/${pid}`);
    return response.data;
};

export const createPayment = async (
    payment: PaymentCreateRequest,
): Promise<PaymentRetrieve> => {
    const response = await portalsApi.post("crm/payments", payment);
    return response.data;
};

export const uploadVoucher = async (file: UploadFile) => {
    const formData = new FormData();
    formData.append("file", file as unknown as Blob);
    const response = await portalsApi.post("crm/payments/upload-voucher", formData);
    return response.data;
};

export const removeVoucher = async (fid: string) => {
    const response = await portalsApi.delete(`crm/payments/remove-voucher/${fid}`);
    return response.data;
};
