import { App, DraggerProps, Image, Typography, Upload, UploadFile } from "antd";
import { CloudUpload } from "lucide-react";
import { RcFile } from "antd/es/upload";
import {
    removeEventScheduleCoverImage,
    uploadEventScheduleCoverImage,
} from "../../services/portals/event-schedule";
import ImgCrop from "antd-img-crop";
import { useState } from "react";
import { EventCoverImage } from "../../types/event";

const { Dragger } = Upload;
const { Text } = Typography;

interface UploadEventScheduleCoverProps {
    esid: string;
    initialEventScheduleCoverImage?: EventCoverImage;
}

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const IMAGE_WIDTH = 1193;
const IMAGE_HEIGHT = 466;
const ASPECT_RATIO = IMAGE_WIDTH / IMAGE_HEIGHT;

export default function UploadEventScheduleCover({
    esid,
    initialEventScheduleCoverImage,
}: UploadEventScheduleCoverProps) {
    const { notification } = App.useApp();

    const [previewOpen, setPreviewOpen] = useState(false);
    const [fileList, setFileList] = useState<UploadFile[]>(
        initialEventScheduleCoverImage
            ? [
                  {
                      uid: initialEventScheduleCoverImage.fid,
                      name: initialEventScheduleCoverImage.name,
                      url: initialEventScheduleCoverImage.url,
                      status: "done",
                  },
              ]
            : [],
    );

    const draggerProps: DraggerProps = {
        multiple: false,
        maxCount: 1,
        accept: ".jpg, .jpeg, .png, .webp",
        listType: "picture",
        customRequest: async (options) => {
            const { file, onSuccess, onError } = options;
            try {
                // Define proper types for file
                const fileToUpload = file as RcFile & Partial<UploadFile>;
                const fileRes = await uploadEventScheduleCoverImage(esid, fileToUpload);
                if (fileRes && onSuccess) {
                    // Update the file's uid to the fid returned by UploadEventScheduleCover
                    const updatedFile = { ...fileToUpload, uid: fileRes.fid };
                    onSuccess(fileRes, updatedFile);
                    notification.success({
                        message: "Archivo subido correctamente",
                        duration: 2,
                    });
                }
            } catch (error) {
                if (onError)
                    onError(
                        new Error(
                            error instanceof Error ? error.message : "Unknown error",
                        ),
                        file,
                    );
            }
        },
        onChange(info) {
            setFileList(info.fileList);
        },
        beforeUpload: (file) => {
            const isLower = file.size / 1024 / 1024 < MAX_FILE_SIZE;
            if (!isLower) {
                notification.error({
                    message: "El archivo debe ser menor a 10MB  o .webp",
                    duration: 2,
                });
            }
            return isLower;
        },
        onPreview: async () => {
            setPreviewOpen(true);
        },
        onRemove: async (file) => {
            const fid = file?.response?.fid || file?.uid;
            try {
                const res = await removeEventScheduleCoverImage(esid, fid);
                console.log(res);
                notification.success({
                    message: "Archivo eliminado correctamente",
                    duration: 2,
                });
            } catch (error) {
                notification.error({
                    message: "Error al eliminar el archivo",
                    duration: 2,
                });
            }
        },
        showUploadList: {
            showDownloadIcon: true,
            showRemoveIcon: true,
            showPreviewIcon: true,
        },
        fileList: fileList,
    };

    return (
        <>
            <ImgCrop aspect={ASPECT_RATIO}>
                <Dragger {...draggerProps}>
                    <div className="flex flex-col justify-center items-center">
                        <CloudUpload />
                        <Text className="font-medium text-black-full">
                            Arrastre una imagen aquí
                        </Text>
                        <Text className="text-xs text-black-medium">
                            Solo una imagen (Máx. 10MB)
                        </Text>
                    </div>
                </Dragger>
            </ImgCrop>
            <Image
                wrapperStyle={{ display: "none" }}
                preview={{
                    visible: previewOpen,
                    onVisibleChange: setPreviewOpen,
                }}
                src={fileList[0]?.url}
            />
        </>
    );
}
