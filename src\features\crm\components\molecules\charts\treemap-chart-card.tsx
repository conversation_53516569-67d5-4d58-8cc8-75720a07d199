import { Card } from "antd";
import { CardProps } from "antd/es/card";
import { Responsive<PERSON>ontainer, Treema<PERSON>, Tooltip } from "recharts";

interface TreeMapCardProps extends CardProps {
    data: Array<Record<string, unknown>>;
    valueKey?: string;
    nameKey?: string;
    colors: string[];
    icon?: React.ReactNode;
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        value: number;
        dataKey: string;
        name: string;
        color: string;
    }>;
    label?: string;
}

const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
        const { name, value } = payload[0];
        return (
            <div className="bg-white-full p-2 border border-gray-200 shadow rounded">
                <div className="font-medium">{name}</div>
                <div>{value}</div>
            </div>
        );
    }
    return null;
};

interface CustomTreemapContent {
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    name?: string;
    value?: number;
    color?: string;
}

const CustomTreemapContent = (props: CustomTreemapContent) => {
    const {
        x = 0,
        y = 0,
        width = 0,
        height = 0,
        name = "",
        value = 0,
        color = "",
    } = props;
    return (
        <g>
            <rect
                x={x}
                y={y}
                width={width}
                height={height}
                style={{ fill: color, stroke: "#fff" }}
            />
            {width > 60 && height > 30 && (
                <>
                    <text
                        x={x + width / 2}
                        y={y + height / 2 - 6}
                        textAnchor="middle"
                        fill="#fff"
                        fontWeight={600}
                        fontSize={14}
                    >
                        {name}
                    </text>
                    <text
                        x={x + width / 2}
                        y={y + height / 2 + 12}
                        textAnchor="middle"
                        fill="#fff"
                        fontSize={12}
                    >
                        {value}
                    </text>
                </>
            )}
        </g>
    );
};

const TreeMapCard: React.FC<TreeMapCardProps> = ({
    data,
    valueKey = "value",
    nameKey = "name",
    colors,
    icon,
    ...cardProps
}) => {
    // Recharts espera un array plano para Treemap
    const treemapData = data.map((item, idx) => ({
        name: item[nameKey],
        value: item[valueKey] ?? 0,
        color: colors[idx % colors.length],
    }));

    return (
        <Card
            {...cardProps}
            title={
                <div className="flex items-center gap-2 w-full">
                    {icon}
                    <span>{cardProps.title || ""}</span>
                </div>
            }
            className="shadow-md h-full"
            styles={{ body: { height: 350, padding: 16 } }}
        >
            <ResponsiveContainer width="100%" height="100%">
                <Treemap
                    data={treemapData}
                    dataKey={valueKey}
                    nameKey={nameKey}
                    stroke="#fff"
                    content={<CustomTreemapContent />}
                    isAnimationActive={false}
                >
                    <Tooltip content={<CustomTooltip />} />
                </Treemap>
            </ResponsiveContainer>
        </Card>
    );
};

export default TreeMapCard;
