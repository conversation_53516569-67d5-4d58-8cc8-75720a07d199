import { But<PERSON>, <PERSON>, Modal, Steps, StepsProps, Tooltip } from "antd";
import SelectContact from "@/features/crm/components/molecules/select-contact";
import {
    OrderPartialUpdate,
    OrderStage,
    OrderStageLabels,
    PartialUpdateOrderValues,
    RetrieveOrder,
} from "@/features/crm/types/order";
import { formatDateTime } from "@lib/helpers";
import OrderItemsTable from "./order-items-table";
import SelectStaffUser from "@/features/crm/components/molecules/select-staff-user";
import { Link2, PlusCircle, Save, Trash } from "lucide-react";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import SelectOrderBenefits from "../molecules/select-order-benefits";
import SelectOrderLeadSources from "../molecules/select-order-lead-sources";
import EditStageDatePopover from "../molecules/edit-stage-date";
import EditOrderStage from "../molecules/edit-order-stage";
import { useMemo, useState } from "react";
import AddOrderItemForm from "./add-order-item-form";
import { Link } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";
import { partialUpdateOrder } from "../../services/portals/order";

type EditOrderFormProps = {
    order: RetrieveOrder;
};

export default function EditOrderForm({ order }: EditOrderFormProps) {
    const [form] = Form.useForm<PartialUpdateOrderValues>();

    const regularStepItems: StepsProps["items"] = [
        OrderStage.PROSPECT,
        OrderStage.INTERESTED,
        OrderStage.TO_PAY,
        OrderStage.SOLD,
    ].map((stageType) => {
        const stageInfo = order?.stagesDates.find((s) => s.stage === stageType);

        return {
            title: OrderStageLabels[stageType],
            status: stageType === order.stage ? "finish" : "wait",
            description: (
                <div className="flex flex-col gap-1">
                    {stageInfo && (
                        <span className="text-xs text-gray-500">
                            {formatDateTime(stageInfo.date)}
                        </span>
                    )}
                    <div className="flex items-center justify-between">
                        {stageType === order.stage ? (
                            <span className="flex items-center gap-1">
                                <span className="text-blue-full font-medium text-xs">
                                    Actual
                                </span>
                                <EditStageDatePopover stage={stageType} order={order} />
                            </span>
                        ) : (
                            <EditOrderStage
                                stage={stageType}
                                currentStage={order.stage}
                                order={order}
                            />
                        )}
                    </div>
                </div>
            ),
        };
    });
    const lostStageInfo = order?.stagesDates.find(
        (s) => s.stage === OrderStage.LOST && s.date,
    );
    const lostStagItems: StepsProps["items"] = useMemo(
        () =>
            lostStageInfo
                ? [
                      {
                          title: OrderStageLabels[OrderStage.LOST],
                          status: order.stage === OrderStage.LOST ? "error" : "wait",
                          description: (
                              <div className="flex flex-col gap-1">
                                  {lostStageInfo && (
                                      <span className="text-xs text-gray-500">
                                          {formatDateTime(lostStageInfo.date)}
                                      </span>
                                  )}
                                  <div className="flex items-center justify-between">
                                      {order.stage === OrderStage.LOST ? (
                                          <span className="flex items-center gap-1">
                                              <span className="text-red-500 font-medium text-xs">
                                                  Actual
                                              </span>
                                              <EditStageDatePopover
                                                  stage={OrderStage.LOST}
                                                  order={order}
                                              />
                                          </span>
                                      ) : (
                                          <EditOrderStage
                                              stage={OrderStage.LOST}
                                              currentStage={order.stage}
                                              order={order}
                                          />
                                      )}
                                  </div>
                              </div>
                          ),
                      },
                  ]
                : [],
        [lostStageInfo, order],
    );

    const stepItems: StepsProps["items"] = useMemo(
        () =>
            lostStagItems ? [...regularStepItems, ...lostStagItems] : regularStepItems,
        [regularStepItems, lostStagItems],
    );

    const [modalAddOrderItemOpen, setModalAddOrderItemOpen] = useState(false);

    const handleAddOrderItem: React.MouseEventHandler<SVGSVGElement> = () => {
        setModalAddOrderItemOpen(true);
    };

    const contact = Form.useWatch(["contact"], form);

    const { mutate: updateOrderMutate, isPending } = useMutation({
        mutationKey: ["update-order", order.oid],
        mutationFn: (payload: OrderPartialUpdate) =>
            partialUpdateOrder(order.oid, payload),
        onSuccess: () => {
            form.resetFields();
        },
    });

    const handleFormFinish = (values: PartialUpdateOrderValues) => {
        updateOrderMutate(values);
    };

    return (
        <>
            <Form
                name="edit-order-form"
                layout="vertical"
                form={form}
                initialValues={{
                    owner: order?.owner.uid,
                    salesAgent: order?.salesAgent?.uid,
                    benefits: order?.benefits.map((benefit) => benefit?.bid),
                    leadSources: order?.leadSources.map(
                        (leadSource) => leadSource?.lsid,
                    ),
                    agreedTotal: order?.agreedTotal,
                }}
                onFinish={handleFormFinish}
            >
                <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                    <div className="col-span-4 space-y-6">
                        <div className="bg-white-full rounded-lg shadow-sm p-4">
                            <p className="text-gray-400 font-semibold text-sm">
                                INFORMACIÓN GENERAL
                            </p>
                            <Steps items={stepItems} className="my-6" />
                            <div className="flex items-center gap-2">
                                <Form.Item<PartialUpdateOrderValues>
                                    name="owner"
                                    label={<FormLabel>Contacto</FormLabel>}
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                "Por favor, seleccione algún contacto.",
                                        },
                                    ]}
                                    className="w-full"
                                >
                                    <SelectContact />
                                </Form.Item>
                                <Tooltip title="Abrir contacto">
                                    <Link
                                        to={`/crm/contacts/${contact}`}
                                        type="link"
                                        className="text-blue-full text-xs font-semibold mt-2"
                                    >
                                        <Link2 size={14} /> Abrir
                                    </Link>
                                </Tooltip>
                            </div>

                            <Form.Item<PartialUpdateOrderValues>
                                name="benefits"
                                label={<FormLabel>Beneficios</FormLabel>}
                            >
                                <SelectOrderBenefits />
                            </Form.Item>
                        </div>
                        <div className="bg-white-full rounded-lg shadow-sm p-4">
                            <div className="flex justify-between">
                                <p className="text-gray-400 font-semibold text-sm">
                                    PRODUCTOS
                                </p>
                                <PlusCircle
                                    className="text-blue-full hover:cursor-pointer hover:text-blue-700"
                                    onClick={handleAddOrderItem}
                                />
                            </div>
                            <OrderItemsTable products={order?.orderItems} />
                        </div>
                    </div>
                    <div className="col-span-2 space-y-6">
                        <div className="bg-white-full p-5 rounded-lg shadow-sm">
                            <p className="text-gray-400 font-semibold text-sm">
                                ACCIONES
                            </p>
                            <div className="flex flex-col gap-3">
                                <div className="flex gap-3 justify-end">
                                    <Button
                                        type="primary"
                                        size="large"
                                        style={{ fontSize: 16 }}
                                        icon={<Trash />}
                                        danger
                                        disabled={isPending}
                                    >
                                        Eliminar
                                    </Button>
                                    <Button
                                        type="primary"
                                        size="large"
                                        style={{ fontSize: 16 }}
                                        icon={<Save />}
                                        htmlType="submit"
                                        loading={isPending}
                                    >
                                        Guardar
                                    </Button>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white-full p-5 rounded-lg shadow-sm">
                            <p className="text-gray-400 font-semibold text-sm">
                                INFORMACIÓN ADICIONAL
                            </p>

                            <Form.Item<PartialUpdateOrderValues>
                                name="salesAgent"
                                label={<FormLabel>Agente de ventas</FormLabel>}
                            >
                                <SelectStaffUser />
                            </Form.Item>

                            <Form.Item<PartialUpdateOrderValues>
                                name="leadSources"
                                label={<FormLabel>Fuentes del lead</FormLabel>}
                            >
                                <SelectOrderLeadSources />
                            </Form.Item>
                        </div>
                    </div>
                </div>
            </Form>
            <Modal
                title="Agregar producto"
                open={modalAddOrderItemOpen}
                onCancel={() => setModalAddOrderItemOpen(false)}
                centered
                width={540}
            >
                <AddOrderItemForm />
            </Modal>
        </>
    );
}
